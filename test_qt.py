# -*- coding: utf-8 -*-
"""
Qt 导入测试脚本
用于诊断 QWidget 创建问题
"""
import sys
import os
from loguru import logger

def test_qt_import():
    """测试Qt导入"""
    try:
        logger.info("步骤1: 导入 PySide6.QtWidgets")
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        logger.info("步骤1: 成功")

        logger.info("步骤2: 设置 Qt 属性")
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_ShareOpenGLContexts)
        logger.info("步骤2: 成功")

        logger.info("步骤3: 创建 QApplication")
        app = QApplication(sys.argv)
        logger.info("步骤3: 成功")

        logger.info("步骤4: 测试导入 qfluentwidgets")
        try:
            import qfluentwidgets
            logger.info("步骤4: qfluentwidgets 导入成功")
        except Exception as e:
            logger.error(f"步骤4: qfluentwidgets 导入失败: {e}")

        logger.info("步骤5: 测试导入主窗口模块")
        try:
            from src.window.firefly.FireflyWindow import MainWindow
            logger.info("步骤5: 主窗口模块导入成功")
        except Exception as e:
            logger.error(f"步骤5: 主窗口模块导入失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

        logger.info("步骤6: 创建主窗口")
        try:
            window = MainWindow(app)
            logger.info("步骤6: 主窗口创建成功")
            window.show()
            logger.info("步骤7: 主窗口显示成功")
        except Exception as e:
            logger.error(f"步骤6-7: 主窗口创建/显示失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

        logger.info("所有测试通过，启动应用...")
        return app.exec()

    except Exception as e:
        logger.error(f"Qt 测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    logger.info("开始 Qt 导入测试...")
    result = test_qt_import()
    logger.info(f"测试结果: {result}")
    sys.exit(0 if result else 1)
