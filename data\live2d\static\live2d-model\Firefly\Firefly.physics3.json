{"Version": 3, "Meta": {"PhysicsSettingCount": 26, "TotalInputCount": 40, "TotalOutputCount": 39, "VertexCount": 56, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "墨镜"}, {"Id": "PhysicsSetting2", "Name": "问号"}, {"Id": "PhysicsSetting3", "Name": "开心"}, {"Id": "PhysicsSetting4", "Name": "生气"}, {"Id": "PhysicsSetting5", "Name": "呆愣"}, {"Id": "PhysicsSetting6", "Name": "腿"}, {"Id": "PhysicsSetting7", "Name": "头饰1"}, {"Id": "PhysicsSetting8", "Name": "L猫耳"}, {"Id": "PhysicsSetting9", "Name": "R猫耳"}, {"Id": "PhysicsSetting10", "Name": "刘海"}, {"Id": "PhysicsSetting11", "Name": "刘海3"}, {"Id": "PhysicsSetting12", "Name": "右侧发"}, {"Id": "PhysicsSetting13", "Name": "R侧发"}, {"Id": "PhysicsSetting14", "Name": "L侧发"}, {"Id": "PhysicsSetting15", "Name": "后发"}, {"Id": "PhysicsSetting16", "Name": "刘海PH"}, {"Id": "PhysicsSetting17", "Name": "刘海3PH"}, {"Id": "PhysicsSetting18", "Name": "右侧发PH"}, {"Id": "PhysicsSetting19", "Name": "R侧发PH"}, {"Id": "PhysicsSetting20", "Name": "L侧发PH"}, {"Id": "PhysicsSetting21", "Name": "披风PHY"}, {"Id": "PhysicsSetting22", "Name": "胸衣XPHY"}, {"Id": "PhysicsSetting23", "Name": "捂手PHY"}, {"Id": "PhysicsSetting24", "Name": "变身PHY"}, {"Id": "PhysicsSetting25", "Name": "拍手PHY"}, {"Id": "PhysicsSetting26", "Name": "思考PHY"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "Param"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param6"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 1.2, "Acceleration": 4, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "Param5"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param7"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 1.2, "Acceleration": 4, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "Param11"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param12"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.95, "Delay": 1, "Acceleration": 4, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "Param4"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param13"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 1, "Acceleration": 4, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "Param10"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param14"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 1, "Acceleration": 4, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param25"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param29"}, "VertexIndex": 2, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 40}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 40}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.95, "Delay": 0.6, "Acceleration": 1, "Radius": 20}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param26"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.6, "Acceleration": 0.6, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param27"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 5, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param28"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 5, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param16"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 5, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.3, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param22"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.3, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param24"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.3, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param18"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.3, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param20"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.3, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param30"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param32"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param34"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param35"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param37"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param39"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param31"}, "VertexIndex": 2, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param33"}, "VertexIndex": 2, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param36"}, "VertexIndex": 2, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param38"}, "VertexIndex": 2, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.3, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.65, "Acceleration": 0.3, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param15"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.6, "Acceleration": 0.5, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.6, "Acceleration": 0.6, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param21"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.6, "Acceleration": 0.6, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param23"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.6, "Acceleration": 0.6, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param17"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.6, "Acceleration": 0.6, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param19"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.6, "Acceleration": 0.6, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param41"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param42"}, "VertexIndex": 2, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 0.4, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 1, "Acceleration": 0.4, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.4, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param43"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param44"}, "VertexIndex": 2, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 0.4, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 1, "Acceleration": 0.4, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.4, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "Param46"}, "Weight": 80, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param47"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.6, "Delay": 0.6, "Acceleration": 10, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.6, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting24", "Input": [{"Source": {"Target": "Parameter", "Id": "Param45"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param48"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.6, "Delay": 0.6, "Acceleration": 10, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.6, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting25", "Input": [{"Source": {"Target": "Parameter", "Id": "Param49"}, "Weight": 80, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param50"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param51"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.6, "Delay": 0.6, "Acceleration": 10, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.6, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting26", "Input": [{"Source": {"Target": "Parameter", "Id": "Param52"}, "Weight": 80, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param53"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.6, "Delay": 0.6, "Acceleration": 10, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.6, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}