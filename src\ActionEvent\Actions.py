import os
import json
from typing import Any
from PySide6.QtCore import Signal, QThread
from loguru import logger


class ActionPic:
    def __init__(self) -> None:
        self.filePath = os.path.join(
            os.getcwd(), "data", "config", "action_pictures.json"
        )
        if not os.path.isfile(self.filePath):
            with open(self.filePath, "w+", encoding="utf-8") as wfp:
                wfp.write(json.dumps({}))
        self.actions = []
        self._cache = {}  # 添加缓存
    
    def read(self, key: str) -> list:
        """读取指定动作的图片文件列表，使用缓存提高性能"""
        # 检查缓存
        if key in self._cache:
            return self._cache[key].copy()

        try:
            with open(self.filePath, "r", encoding="utf-8") as rfp:
                data = json.load(rfp)

            # 尝试查找Key
            result = data.get(key)
            if result is None:
                logger.error(f"未找到动作配置: {key}")
                self._cache[key] = []
                return []

            # 将数据填入action
            path = result.get("path")
            if not path:
                logger.error(f"动作配置 {key} 缺少路径信息")
                self._cache[key] = []
                return []

            if not os.path.exists(path):
                logger.error(f"动作路径不存在: {path}")
                self._cache[key] = []
                return []

            actions = []
            try:
                # 只扫描一层目录，提高性能
                if os.path.isdir(path):
                    files = os.listdir(path)
                    for file in sorted(files):  # 排序确保一致性
                        if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                            actions.append(os.path.join(path, file))
                else:
                    logger.error(f"动作路径不是目录: {path}")

            except OSError as e:
                logger.error(f"读取动作文件夹时出错 {path}: {e}")
                self._cache[key] = []
                return []

            if not actions:
                logger.warning(f"动作文件夹为空: {path}")

            # 缓存结果
            self._cache[key] = actions
            return actions.copy()

        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"读取动作配置文件失败: {e}")
            self._cache[key] = []
            return []
        except Exception as e:
            logger.error(f"读取动作配置时出现未知错误: {e}")
            self._cache[key] = []
            return []


# 全局实例
actionPic = ActionPic()

# 懒加载动作数据
class ActionDataManager:
    def __init__(self):
        self._data = {}
        self._action_keys = [
            "Standby", "mention", "sleep", "discomfort",
            "left", "right", "eat", "love"
        ]

    def get(self, key: str) -> list:
        """获取动作数据，懒加载"""
        if key not in self._data:
            self._data[key] = actionPic.read(key)
        return self._data[key].copy()

    def reload(self, key: str) -> list:
        """重新加载指定动作数据"""
        # 清除缓存
        if key in actionPic._cache:
            del actionPic._cache[key]
        if key in self._data:
            del self._data[key]
        return self.get(key)

    def keys(self) -> list:
        """获取所有动作键"""
        return self._action_keys.copy()

actionAllPicData = ActionDataManager()


class ActionEvent(QThread):
    """动作事件"""
    result = Signal(str)
    startActionEventTimerSignal = Signal()
    stopActionEventTimerSignal = Signal()

    def __init__(self, switchBackgroundFunc: Any) -> None:
        """
        对当前已有动作进行加载/播放
        :param switchBackgroundFunc: Any 更改主界面gui背景方法
        :return None
        """
        super().__init__()
        self.switchBackground = switchBackgroundFunc
        self._request_interruption = False  # 使用下划线前缀避免与QThread方法冲突
        self.sign = "Standby"
        self.actionEventPicList = []
        self.current_index = 0  # 使用索引而不是 pop/append

    @property
    def requestInterruption(self) -> bool:
        return self._request_interruption
    
    @requestInterruption.setter
    def requestInterruption(self, value: bool) -> None:
        self._request_interruption = value

    def run(self) -> None:
        """运行"""
        self.startActionEventTimerSignal.emit()

    def playNextImage(self) -> None:
        """播放下一张图片，使用索引提高性能"""
        if self.requestInterruption:
            return

        # 检查是否有图片数据
        if not self.actionEventPicList:
            self.actionEventPicList = actionAllPicData.reload(self.sign)
            self.current_index = 0
            if not self.actionEventPicList:
                logger.warning(f"动作 {self.sign} 没有可用的图片")
                return
            self.result.emit(self.sign)
            return

        # 使用索引获取当前图片
        if self.current_index >= len(self.actionEventPicList):
            self.current_index = 0

        next_image = self.actionEventPicList[self.current_index]
        self.switchBackground(next_image)

        # 对于持续动作，循环播放；对于非持续动作，播放一次
        non_continuous_actions = {'left', 'right', 'eat', 'love'}
        if self.sign in non_continuous_actions:
            self.current_index += 1
            if self.current_index >= len(self.actionEventPicList):
                # 非持续动作播放完毕
                self.result.emit(self.sign)
                self.current_index = 0
        else:
            # 持续动作循环播放
            self.current_index = (self.current_index + 1) % len(self.actionEventPicList)

    def load(self, key: str) -> None:
        """
        通过key加载指定动作的图片列表
        :param key: str
        :return None
        """
        if self.sign != key:  # 只有当动作改变时才重新加载
            self.sign = key
            self.actionEventPicList = actionAllPicData.get(key)
            self.current_index = 0

    def mentionEvent(self) -> None:
        """提起"""
        try:
            logger.info("执行提起动作")
            self.load("mention")
        except Exception as e:
            logger.error(f"提起动作失败: {e}")
            # 尝试恢复到待机状态
            try:
                self.load("Standby")
            except Exception as recovery_error:
                logger.error(f"恢复到待机状态失败: {recovery_error}")

    def standbyEvent(self) -> None:
        """待机"""
        try:
            logger.info("执行待机动作")
            self.load("Standby")
        except Exception as e:
            logger.error(f"待机动作失败: {e}")
            # 重置状态
            self.sign = "Standby"
            self.actionEventPicList = []
            self.current_index = 0

    def eatEvent(self) -> None:
        """吃"""
        try:
            logger.info("执行吃饭动作")
            self.load("eat")
        except Exception as e:
            logger.error(f"吃饭动作失败: {e}")
            # 尝试恢复到待机状态
            try:
                self.load("Standby")
            except Exception as recovery_error:
                logger.error(f"恢复到待机状态失败: {recovery_error}")

    def sleepEvent(self) -> None:
        """睡觉"""
        try:
            logger.info("执行睡觉动作")
            self.load("sleep")
        except Exception as e:
            logger.error(f"睡觉动作失败: {e}")
            # 尝试恢复到待机状态
            try:
                self.load("Standby")
            except Exception as recovery_error:
                logger.error(f"恢复到待机状态失败: {recovery_error}")

    def loveEvent(self) -> None:
        """ღ( ´･ᴗ･` )比心"""
        try:
            logger.info("执行摸头动作")
            self.load("love")
        except Exception as e:
            logger.error(f"摸头动作失败: {e}")
            # 尝试恢复到待机状态
            try:
                self.load("Standby")
            except Exception as recovery_error:
                logger.error(f"恢复到待机状态失败: {recovery_error}")

    def discomfortEvent(self) -> None:
        """不适"""
        try:
            logger.info("执行不适动作")
            self.load("discomfort")
        except Exception as e:
            logger.error(f"不适动作失败: {e}")
            # 尝试恢复到待机状态
            try:
                self.load("Standby")
            except Exception as recovery_error:
                logger.error(f"恢复到待机状态失败: {recovery_error}")

    def left(self) -> None:
        """向左"""
        try:
            self.load("left")
        except Exception as e:
            logger.error(f"向左动作失败: {e}")
            # 尝试恢复到待机状态
            try:
                self.load("Standby")
            except Exception as recovery_error:
                logger.error(f"恢复到待机状态失败: {recovery_error}")
    
    def right(self) -> None:
        """向右"""
        try:
            self.load("right")
        except Exception as e:
            logger.error(f"向右动作失败: {e}")
            # 尝试恢复到待机状态
            try:
                self.load("Standby")
            except Exception as recovery_error:
                logger.error(f"恢复到待机状态失败: {recovery_error}")
