# DATA资源使用说明文档

本说明文档梳理了 data 文件夹下所有资源的分类、用途、已用情况、未用资源的集成建议，以及如何扩展/自定义资源。

---

## 1. 图片资源（data/assets/images/）

### 1.1 firefly/actions/
- **内容**：discomfort、eat、left、Love、mention、right、sleep、Standby 等动作帧图片
- **用途**：桌宠动作切换，已在 ActionEvent/Actions.py 及 FireflyWindow.py 中用上

### 1.2 firefly/Angry/、Happy/、Lech/、Love/、Lovely/、Sadness/
- **内容**：各类表情、情绪、特殊动作 gif/png
- **用途**：用于语音包配图、动作切换、情绪反馈，已在 voicepack.json 及主窗口逻辑中用上

### 1.3 firefly/bg/、default/bg.png
- **内容**：背景图片
- **用途**：主窗口背景，已在 FireflyWindow.py 中用上
- **集成建议**：default/bg.png 可作为默认背景，建议主窗口增加"切换背景"功能

### 1.4 firefly/icon/
- **内容**：icon.ico、icon.png
- **用途**：应用窗口icon，已在主窗口设置

### 1.5 firefly/voicePack/
- **内容**：语音包配图
- **用途**：语音包配图，已在 voicepack.json 用上

### 1.6 icon/
- **内容**：battery.png、cake.png、exit.png、extend-icon.png、function-icon.png、setting.png、sleep.png
- **用途**：管理窗口、右键菜单等功能按钮，已在 ManagementWindow.py、menu.py 用上

### 1.7 其他图片
- **learn.png**：建议作为"帮助"按钮icon，集成到管理窗口或帮助界面
- **rotate.gif**：建议作为"旋转"动作或切换动画，集成到动作系统或窗口拖动特效
- **Happy/bar.gif、exceed.gif、expect.gif、feel.gif、give.gif、large.gif、of.gif、see.gif、seek.gif、sleep.gif、stick.gif、surprise.gif、you.gif**：部分未在语音包或动作系统中直接用到，可扩展为更多表情/动作/语音配图
- **Lovely/peeping.png**：建议作为"偷看"动作或特殊互动

---

## 2. 音频资源（data/audio/）
- **内容**：18个语音wav文件
- **用途**：全部已用，详见 VOICE_USAGE_SUMMARY.md

---

## 3. 配置资源（data/config/）
- **内容**：action_pictures.json、battery_voice.json、emoji_pack.json、live2d.config.json、main.json、user_settings.json、voicepack.json
- **用途**：均已在主程序、扩展、Live2D、语音包等功能中用上
- **extends/BatteryVoice/info.json**：用于扩展功能，已在 src/extends/BatteryVoice/main.py 用上

---

## 4. Live2D资源（data/live2d/）

### 4.1 static/assets/、static/audio/
- **内容**：Live2D前端静态资源、音频
- **用途**：用于 Live2D web 端渲染，已在 Live2dHttpServer.py、Live2dEngineView.py 用上
- **static/audio/chun/test.mp3**：建议 Live2D模型"椿"增加音效互动

### 4.2 static/live2d-model/
- **内容**：Firefly、chun 两套Live2D模型及贴图、动作、表情、音频等
- **用途**：Firefly模型已用，chun模型资源已存在但 live2d.config.json 默认未启用
- **集成建议**：管理窗口可增加模型切换功能，支持"椿"模型

### 4.3 templates/
- **内容**：chun.html、firefly.html
- **用途**：Live2D网页模板，已在 Live2dHttpServer.py 用上

---

## 5. 扩展与自定义说明

- **图片/音频/模型资源**：可自由添加，建议在 config/voicepack.json、action_pictures.json、live2d.config.json 等配置文件中注册后使用
- **Live2D模型**：可放入 static/live2d-model/，并在 live2d.config.json 中配置
- **语音包**：可扩展 voicepack.json，支持更多时间段、情绪、互动类型
- **动作/表情**：可在 ActionEvent/Actions.py、RoleProperties.py 中扩展动作逻辑

---

## 6. 资源使用建议

- 未用图片建议补充到动作、语音、互动等场景
- Live2D"椿"模型建议在管理窗口支持切换
- 可增加"帮助"、"旋转"、"偷看"等趣味互动
- 所有资源均可通过配置文件灵活扩展

---

如需进一步扩展资源或功能，请参考本说明文档和项目内 README、DEVELOPER.md 等开发文档。 