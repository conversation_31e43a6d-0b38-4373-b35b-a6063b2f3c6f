#!/bin/bash

echo "========================================"
echo "MyFlowingFireflyWife 构建脚本"
echo "========================================"

# 检查 Python 环境
echo "检查 Python 环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到 Python3，请确保 Python3 已安装"
    exit 1
fi

python3 --version

# 检查依赖
echo "检查依赖..."
if ! python3 -c "import PyInstaller" &> /dev/null; then
    echo "安装 PyInstaller..."
    pip3 install pyinstaller
fi

# 清理旧的构建文件
echo "清理旧的构建文件..."
rm -rf dist build *.spec

# 开始构建
echo "开始构建..."
python3 -m PyInstaller \
    --onefile \
    --windowed \
    --add-data "data:data" \
    --add-data "src:src" \
    --name "MyFlowingFireflyWife" \
    --distpath "dist" \
    --workpath "build" \
    --specpath "." \
    MyFlowingFireflyWife.py

if [ $? -ne 0 ]; then
    echo "构建失败！"
    exit 1
fi

echo "构建完成！"
echo "正在复制必要文件..."

# 复制数据文件
cp -r data dist/

echo "========================================"
echo "构建成功完成！"
echo "可执行文件: dist/MyFlowingFireflyWife"
echo "========================================"
