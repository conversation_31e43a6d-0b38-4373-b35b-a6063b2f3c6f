import logging
from typing import Union, Optional
from PySide6.QtWidgets import (
    QFrame, QMainWindow, QLabel, QLineEdit,
    QHBoxLayout, QWidget, QVBoxLayout, QTabWidget,
    QPushButton, QComboBox, QSlider, QCheckBox,
    QGroupBox, QScrollArea, QGridLayout, QSpinBox,
    QTextEdit, QFileDialog, QMessageBox, QApplication,
    QSplitter, QListWidget, QListWidgetItem
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QIcon
import json
import os
from loguru import logger

logger = logging.getLogger(__name__)

class UniversalWidget(QFrame):
    def __init__(self, text: str, parent=None):
        super().__init__(parent)
        self.setObjectName(text.replace(' ', '-'))
        self.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        layout = QVBoxLayout()
        self.setLayout(layout)
        label = QLabel(text)
        label.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50;")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)

class SettingsWidget(QWidget):
    """设置界面组件"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 基础设置组
        basic_group = QGroupBox("基础设置")
        basic_layout = QGridLayout(basic_group)
        
        # 缩放设置
        basic_layout.addWidget(QLabel("缩放比例:"), 0, 0)
        self.scale_slider = QSlider(Qt.Orientation.Horizontal)
        self.scale_slider.setRange(0, 100)
        self.scale_slider.setValue(0)
        basic_layout.addWidget(self.scale_slider, 0, 1)
        self.scale_label = QLabel("0%")
        basic_layout.addWidget(self.scale_label, 0, 2)
        
        # 语音设置
        self.voice_start_cb = QCheckBox("启动时播放语音")
        basic_layout.addWidget(self.voice_start_cb, 1, 0, 1, 2)
        
        self.voice_close_cb = QCheckBox("关闭时播放语音")
        basic_layout.addWidget(self.voice_close_cb, 2, 0, 1, 2)
        
        # 窗口设置
        self.always_top_cb = QCheckBox("始终置顶")
        self.always_top_cb.setChecked(True)
        basic_layout.addWidget(self.always_top_cb, 3, 0, 1, 2)
        
        self.auto_hide_cb = QCheckBox("自动隐藏（鼠标离开时）")
        basic_layout.addWidget(self.auto_hide_cb, 4, 0, 1, 2)
        
        layout.addWidget(basic_group)
        
        # 动作设置组
        action_group = QGroupBox("动作设置")
        action_layout = QGridLayout(action_group)
        
        # 自动动作间隔
        action_layout.addWidget(QLabel("自动动作间隔(秒):"), 0, 0)
        self.action_interval_spin = QSpinBox()
        self.action_interval_spin.setRange(5, 300)
        self.action_interval_spin.setValue(30)
        action_layout.addWidget(self.action_interval_spin, 0, 1)
        
        # 启用自动动作
        self.auto_action_cb = QCheckBox("启用自动动作")
        self.auto_action_cb.setChecked(True)
        action_layout.addWidget(self.auto_action_cb, 1, 0, 1, 2)
        
        layout.addWidget(action_group)
        
        # 保存按钮
        save_btn = QPushButton("保存设置")
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)
        
        layout.addStretch()
    
    def load_settings(self):
        """加载设置"""
        try:
            config_path = os.path.join(os.getcwd(), "data", "config", "main.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                self.scale_slider.setValue(settings.get('scaling', 0))
                self.voice_start_cb.setChecked(settings.get('is_play_VoiceOnStart', False))
                self.voice_close_cb.setChecked(settings.get('is_play_VoiceOnClose', False))
        except Exception as e:
            logger.error(f"加载设置失败: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            config_path = os.path.join(os.getcwd(), "data", "config", "main.json")
            settings = {
                'scaling': self.scale_slider.value(),
                'is_play_VoiceOnStart': self.voice_start_cb.isChecked(),
                'is_play_VoiceOnClose': self.voice_close_cb.isChecked(),
                'always_top': self.always_top_cb.isChecked(),
                'auto_hide': self.auto_hide_cb.isChecked(),
                'action_interval': self.action_interval_spin.value(),
                'auto_action': self.auto_action_cb.isChecked()
            }
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=4, ensure_ascii=False)
            
            QMessageBox.information(self, "成功", "设置已保存")
        except Exception as e:
            logger.error(f"保存设置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存设置失败: {e}")

class ExtensionsWidget(QWidget):
    """扩展功能界面组件"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.extensions = {}
        self.init_ui()
        self.load_extensions()
    
    def init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 扩展列表
        self.extension_list = QListWidget()
        layout.addWidget(QLabel("可用扩展:"))
        layout.addWidget(self.extension_list)
        
        # 扩展控制按钮
        btn_layout = QHBoxLayout()
        self.start_btn = QPushButton("启动扩展")
        self.start_btn.clicked.connect(self.start_extension)
        self.stop_btn = QPushButton("停止扩展")
        self.stop_btn.clicked.connect(self.stop_extension)
        self.refresh_btn = QPushButton("刷新列表")
        self.refresh_btn.clicked.connect(self.load_extensions)
        
        btn_layout.addWidget(self.start_btn)
        btn_layout.addWidget(self.stop_btn)
        btn_layout.addWidget(self.refresh_btn)
        layout.addLayout(btn_layout)
        
        # 扩展信息
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(150)
        self.info_text.setReadOnly(True)
        layout.addWidget(QLabel("扩展信息:"))
        layout.addWidget(self.info_text)
    
    def load_extensions(self):
        """加载扩展列表"""
        self.extension_list.clear()
        self.extensions.clear()
        
        try:
            extends_dir = os.path.join(os.getcwd(), "data", "config", "extends")
            if os.path.exists(extends_dir):
                for item in os.listdir(extends_dir):
                    info_path = os.path.join(extends_dir, item, "info.json")
                    if os.path.exists(info_path):
                        with open(info_path, 'r', encoding='utf-8') as f:
                            info = json.load(f)
                        
                        self.extensions[item] = info
                        list_item = QListWidgetItem(f"{info.get('name', item)} - {info.get('description', '无描述')}")
                        list_item.setData(Qt.ItemDataRole.UserRole, item)
                        self.extension_list.addItem(list_item)
        except Exception as e:
            logger.error(f"加载扩展失败: {e}")
    
    def start_extension(self):
        """启动选中的扩展"""
        current_item = self.extension_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择一个扩展")
            return
        
        extension_name = current_item.data(Qt.ItemDataRole.UserRole)
        try:
            # 这里应该调用扩展的启动方法
            QMessageBox.information(self, "成功", f"扩展 {extension_name} 已启动")
        except Exception as e:
            logger.error(f"启动扩展失败: {e}")
            QMessageBox.critical(self, "错误", f"启动扩展失败: {e}")
    
    def stop_extension(self):
        """停止选中的扩展"""
        current_item = self.extension_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择一个扩展")
            return
        
        extension_name = current_item.data(Qt.ItemDataRole.UserRole)
        try:
            # 这里应该调用扩展的停止方法
            QMessageBox.information(self, "成功", f"扩展 {extension_name} 已停止")
        except Exception as e:
            logger.error(f"停止扩展失败: {e}")
            QMessageBox.critical(self, "错误", f"停止扩展失败: {e}")

class Live2DWidget(QWidget):
    """Live2D设置界面组件"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.load_live2d_config()
    
    def init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # Live2D开关
        self.enable_live2d_cb = QCheckBox("启用Live2D模型")
        layout.addWidget(self.enable_live2d_cb)
        
        # 模型选择
        model_group = QGroupBox("模型设置")
        model_layout = QGridLayout(model_group)
        
        model_layout.addWidget(QLabel("当前模型:"), 0, 0)
        self.model_combo = QComboBox()
        self.model_combo.addItems(["Firefly", "椿"])
        model_layout.addWidget(self.model_combo, 0, 1)
        
        # 模型路径设置
        model_layout.addWidget(QLabel("模型路径:"), 1, 0)
        self.model_path_edit = QLineEdit()
        self.model_path_edit.setReadOnly(True)
        model_layout.addWidget(self.model_path_edit, 1, 1)
        
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.clicked.connect(self.browse_model_path)
        model_layout.addWidget(self.browse_btn, 1, 2)
        
        layout.addWidget(model_group)
        
        # 显示设置
        display_group = QGroupBox("显示设置")
        display_layout = QGridLayout(display_group)
        
        display_layout.addWidget(QLabel("窗口宽度:"), 0, 0)
        self.width_spin = QSpinBox()
        self.width_spin.setRange(200, 800)
        self.width_spin.setValue(400)
        display_layout.addWidget(self.width_spin, 0, 1)
        
        display_layout.addWidget(QLabel("窗口高度:"), 1, 0)
        self.height_spin = QSpinBox()
        self.height_spin.setRange(200, 800)
        self.height_spin.setValue(600)
        display_layout.addWidget(self.height_spin, 1, 1)
        
        layout.addWidget(display_group)
        
        # 控制按钮
        btn_layout = QHBoxLayout()
        self.save_btn = QPushButton("保存设置")
        self.save_btn.clicked.connect(self.save_live2d_config)
        self.test_btn = QPushButton("测试模型")
        self.test_btn.clicked.connect(self.test_model)
        
        btn_layout.addWidget(self.save_btn)
        btn_layout.addWidget(self.test_btn)
        layout.addLayout(btn_layout)
        
        layout.addStretch()
    
    def load_live2d_config(self):
        """加载Live2D配置"""
        try:
            config_path = os.path.join(os.getcwd(), "data", "config", "live2d.config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                self.enable_live2d_cb.setChecked(config.get('enabled', False))
                current_model = config.get('current-model', 'firefly')
                index = self.model_combo.findText(current_model.title())
                if index >= 0:
                    self.model_combo.setCurrentIndex(index)
                
                self.model_path_edit.setText(config.get('model-path', ''))
                self.width_spin.setValue(config.get('width', 400))
                self.height_spin.setValue(config.get('height', 600))
        except Exception as e:
            logger.error(f"加载Live2D配置失败: {e}")
    
    def save_live2d_config(self):
        """保存Live2D配置"""
        try:
            config_path = os.path.join(os.getcwd(), "data", "config", "live2d.config.json")
            config = {
                'enabled': self.enable_live2d_cb.isChecked(),
                'current-model': self.model_combo.currentText().lower(),
                'model-path': self.model_path_edit.text(),
                'width': self.width_spin.value(),
                'height': self.height_spin.value()
            }
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            QMessageBox.information(self, "成功", "Live2D设置已保存")
        except Exception as e:
            logger.error(f"保存Live2D配置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存Live2D配置失败: {e}")
    
    def browse_model_path(self):
        """浏览模型路径"""
        path = QFileDialog.getExistingDirectory(self, "选择Live2D模型目录")
        if path:
            self.model_path_edit.setText(path)
    
    def test_model(self):
        """测试模型"""
        QMessageBox.information(self, "测试", "Live2D模型测试功能待实现")

class ManagementWindow(QMainWindow):
    def __init__(self, FireflyWindowParent: Union[QMainWindow, None] = None):
        try:
            logger.debug("管理窗口: 步骤1 - 准备调用父类构造函数")
            
            # 检查线程状态
            logger.debug("管理窗口: 步骤1.2 - 检查线程状态")
            from PySide6.QtCore import QThread
            current_thread = QThread.currentThread()
            logger.debug(f"管理窗口: 当前线程: {current_thread}")
            
            # 再次检查 QApplication
            logger.debug("管理窗口: 步骤1.3 - 再次检查 QApplication")
            app = QApplication.instance()
            if app is None:
                logger.error("管理窗口: QApplication 实例不存在")
                raise RuntimeError("QApplication 实例不存在")
            logger.debug("管理窗口: QApplication 实例检查通过")
            
            # 调用 QMainWindow 构造函数
            logger.debug("管理窗口: 步骤1.4 - 调用 QMainWindow 构造函数")
            super().__init__()
            logger.debug("管理窗口: 步骤2 - QMainWindow 构造函数完成")
            
            # 设置窗口属性
            self.setWindowTitle("MyFlowingFireflyWife 管理中心")
            self.resize(960, 640)

            # 创建中央窗口部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)

            # 创建布局
            layout = QVBoxLayout()
            self.setLayout(layout)

            # 创建标签页
            self.tab_widget = QTabWidget()
            layout.addWidget(self.tab_widget)

            logger.debug("管理窗口: 步骤2.1 - 基本窗口结构创建完成")

            # 用于管理 firefly window
            self.SelfFireflyWindow: Optional[QMainWindow] = FireflyWindowParent

            # 创建各个界面
            logger.debug("管理窗口: 步骤3 - 创建界面")
            
            # 主页界面
            home_widget = QWidget()
            home_layout = QVBoxLayout()
            home_widget.setLayout(home_layout)
            
            # 欢迎信息
            welcome_label = QLabel("🌟 MyFlowingFireflyWife 管理中心")
            welcome_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50;")
            welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            home_layout.addWidget(welcome_label)
            
            # 状态信息
            status_group = QGroupBox("系统状态")
            status_layout = QGridLayout(status_group)
            
            status_layout.addWidget(QLabel("应用状态:"), 0, 0)
            self.app_status_label = QLabel("运行中")
            self.app_status_label.setStyleSheet("color: green; font-weight: bold;")
            status_layout.addWidget(self.app_status_label, 0, 1)
            
            status_layout.addWidget(QLabel("当前动作:"), 1, 0)
            self.current_action_label = QLabel("待机")
            status_layout.addWidget(self.current_action_label, 1, 1)
            
            status_layout.addWidget(QLabel("语音状态:"), 2, 0)
            self.voice_status_label = QLabel("正常")
            self.voice_status_label.setStyleSheet("color: green;")
            status_layout.addWidget(self.voice_status_label, 2, 1)
            
            home_layout.addWidget(status_group)
            
            # 快速操作
            quick_group = QGroupBox("快速操作")
            quick_layout = QHBoxLayout(quick_group)
            
            self.refresh_btn = QPushButton("刷新状态")
            self.refresh_btn.clicked.connect(self.refresh_status)
            quick_layout.addWidget(self.refresh_btn)
            
            self.restart_btn = QPushButton("重启应用")
            self.restart_btn.clicked.connect(self.restart_app)
            quick_layout.addWidget(self.restart_btn)
            
            home_layout.addWidget(quick_group)
            home_layout.addStretch()
            
            self.tab_widget.addTab(home_widget, "主页")
            
            # 设置界面
            settings_widget = SettingsWidget()
            self.tab_widget.addTab(settings_widget, "设置")
            
            # 扩展界面
            extensions_widget = ExtensionsWidget()
            self.tab_widget.addTab(extensions_widget, "扩展")
            
            # Live2D界面
            live2d_widget = Live2DWidget()
            self.tab_widget.addTab(live2d_widget, "Live2D")

            logger.debug("管理窗口: 步骤11 - 管理窗口创建完成")
            
            # 设置定时器更新状态
            self.status_timer = QTimer()
            self.status_timer.timeout.connect(self.update_status)
            self.status_timer.start(2000)  # 每2秒更新一次

        except Exception as e:
            logger.error(f"管理窗口创建失败: {e}")
            import traceback as tb
            logger.error(f"错误详情: {tb.format_exc()}")
            raise
    
    def refresh_status(self):
        """刷新状态"""
        self.update_status()
        QMessageBox.information(self, "成功", "状态已刷新")
    
    def restart_app(self):
        """重启应用"""
        reply = QMessageBox.question(self, "确认", "确定要重启应用吗？", 
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            # 这里应该实现重启逻辑
            QMessageBox.information(self, "提示", "重启功能待实现")
    
    def update_status(self):
        """更新状态显示"""
        try:
            # 这里应该获取实际的系统状态
            if self.SelfFireflyWindow:
                # 更新应用状态
                self.app_status_label.setText("运行中")
                self.app_status_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.app_status_label.setText("未连接")
                self.app_status_label.setStyleSheet("color: red; font-weight: bold;")
        except Exception as e:
            logger.error(f"更新状态失败: {e}") 