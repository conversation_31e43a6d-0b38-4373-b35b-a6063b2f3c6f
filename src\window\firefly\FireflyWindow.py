import os
import random
import time
from typing import Optional, Union

from loguru import logger
from PySide6.QtGui import QPixmap, QMouseEvent, QPainter, QColor, QPen, QCursor
from PySide6.QtWidgets import (
    QMainWindow,
    QLabel,
    QApplication,
    QGraphicsOpacityEffect
)
from PySide6.QtCore import Qt, QTimer, QRect, Signal, QPropertyAnimation, QEasingCurve, QPoint

from src.window.firefly.FireflyWindowConfig import ConfigFile
from src.ActionEvent import ActionEvent
from src.window.message.interface import PopupInterface, DEF_IMG
from src.FireflyVoicePack.main import FireflyVoicePackQThread
from src.ActionEvent.RoleProperties import Firefly as FireflyRole
from src.window.firefly.menu import Menu


class MainWindow(QMainWindow):
    startActionEventTimerSignal = Signal()
    stopActionEventTimerSignal = Signal()
    actionChanged = Signal(str)  # 新增：动作改变信号

    def __init__(self, app: QApplication) -> None:
        """主窗口"""
        super().__init__()
        self.app = app
        self.mainConfigFileObject = ConfigFile()    # 用于读取主窗口的配置文件

        self.currentBgImage = self.mainConfigFileObject.data.get(
            "currentBgImage", DEF_IMG)               # 当前背景图片
        self.scaling = self.mainConfigFileObject.data.get(
            "scaling", 0)                   # 背景图片缩小比例
        self.isPlayVoiceOnStart: bool = self.mainConfigFileObject.data.get(
            "is_play_VoiceOnStart", False)  # 是否播放程序启动音频
        self.isPlayVoiceOnClose: bool = self.mainConfigFileObject.data.get(
            "is_play_VoiceOnClose", False)  # 是否播放程序结束音频
        
        # 新增配置项
        self.always_top = self.mainConfigFileObject.data.get("always_top", True)
        self.auto_hide = self.mainConfigFileObject.data.get("auto_hide", False)
        self.action_interval = self.mainConfigFileObject.data.get("action_interval", 30)
        self.auto_action = self.mainConfigFileObject.data.get("auto_action", True)
        
        self.isFreeWalking = False  # 用于判断当前是否正在移动
        self.walkingDirection = random.choice(["left", "right"])    # 移动事件，从那个方向走动
        self.drag_pos = None  # 初始化拖拽位置变量
        
        # 新增状态变量
        self.current_action = "Standby"  # 当前动作
        self.last_interaction_time = time.time()  # 最后交互时间
        self.is_hovering = False  # 鼠标悬停状态
        self.auto_action_timer = None  # 自动动作定时器

        # 语言包配置
        self.fireflyVoicePackThread = None
        self.fireflyRoleObject = FireflyRole()

        # 初始化action event
        self.actionEventQThread = ActionEvent(self.switchBackground)
        self.actionEventQThread.result.connect(self.ActionEventMethod)
        self.actionEventQThread.startActionEventTimerSignal.connect(self.startTimer)
        self.actionEventQThread.stopActionEventTimerSignal.connect(self.stopTimer)
        self.actionEventQThread.start()

        # 设置无边框、窗口始终置顶、窗口透明、标题
        flags = Qt.WindowType.FramelessWindowHint | Qt.WindowType.Tool
        if self.always_top:
            flags |= Qt.WindowType.WindowStaysOnTopHint
        self.setWindowFlags(flags)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)  # 显示时不激活窗口
        self.setAttribute(Qt.WidgetAttribute.WA_Hover)  # 启用悬停事件
        self.setWindowTitle("MyFlowingFireflyWife")

        # 加载并显示指定图片，并缩小指定倍
        self.label = QLabel(self)
        self.switchBackground(self.currentBgImage)

        # 右键菜单
        self.RightClickMenu = Menu(self)
        
        # 初始化自动动作系统
        self.init_auto_action_system()
        
        # 设置鼠标跟踪
        self.setMouseTracking(True)

    def init_auto_action_system(self):
        """初始化自动动作系统"""
        if self.auto_action:
            self.auto_action_timer = QTimer(self)
            self.auto_action_timer.timeout.connect(self.perform_random_action)
            self.auto_action_timer.start(self.action_interval * 1000)  # 转换为毫秒

    def perform_random_action(self):
        """执行随机动作"""
        if self.isFreeWalking or self.current_action != "Standby":
            return
        
        # 根据时间选择不同的动作
        current_hour = time.localtime().tm_hour
        if 22 <= current_hour or current_hour <= 6:
            # 夜间倾向于睡觉
            actions = ["sleep", "sleep", "sleep", "discomfort"]
        elif 12 <= current_hour <= 14:
            # 中午倾向于吃饭
            actions = ["eat", "eat", "eat", "discomfort"]
        else:
            # 其他时间随机动作
            actions = ["discomfort", "love", "sleep", "eat"]
        
        action = random.choice(actions)
        self.execute_action(action)
        
        # 随机播放日常语音
        if random.random() < 0.3:  # 30%概率播放语音
            self.play_random_voice("Daily")

    def play_random_voice(self, voice_type: str):
        """播放随机语音"""
        try:
            self.playFireflyVoice(voice_type)
        except Exception as e:
            logger.error(f"播放随机语音失败: {e}")

    def execute_action(self, action: str):
        """执行指定动作"""
        try:
            if action == "sleep":
                self.actionEventQThread.sleepEvent()
            elif action == "eat":
                self.actionEventQThread.eatEvent()
            elif action == "love":
                self.actionEventQThread.loveEvent()
            elif action == "discomfort":
                self.actionEventQThread.discomfortEvent()
            
            self.current_action = action
            self.actionChanged.emit(action)
            logger.info(f"执行动作: {action}")
            
            # 动作执行后随机播放交互语音
            if random.random() < 0.4:  # 40%概率播放交互语音
                self.play_random_voice("Interaction")
                
        except Exception as e:
            logger.error(f"执行动作失败: {e}")

    def contextMenuEvent(self, event):
        """右键菜单事件"""
        try:
            logger.info("右键菜单事件触发")
            if hasattr(self, 'RightClickMenu') and self.RightClickMenu:
                return self.RightClickMenu.contextMenuEvent(event)
            else:
                logger.error("RightClickMenu 未初始化")
        except Exception as e:
            logger.error(f"右键菜单事件处理失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")

    def mouseMoveEvent(self, event: QMouseEvent) -> None:
        """重写 mouseMoveEvent 以实现窗口拖动"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.drag_pos is not None:
            self.move(self.pos() + event.globalPos() - self.drag_pos)
            self.drag_pos = event.globalPos()
            event.accept()
        return super().mouseMoveEvent(event)

    def mousePressEvent(self, event: QMouseEvent) -> None:
        """
        重写 mousePressEvent
        :return None
        """
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_pos = event.globalPos()
            event.accept()
            
            # 更新最后交互时间
            self.last_interaction_time = time.time()
            
            # 界面上半部分为摸头触发区
            trigger_height = self.height() / 4  # 触发区高度占窗口一半
            trigger_area = QRect(0, 0, self.width(), int(trigger_height))

            # 检查点击是否在摸头触发区域内
            if trigger_area.contains(event.pos()):
                self.actionEventQThread.loveEvent()  # 执行摸头动作
                self.current_action = "love"
            else:
                # 切换提起动作
                self.actionEventQThread.mentionEvent()
                self.current_action = "mention"
            
            self.actionChanged.emit(self.current_action)
        return super().mousePressEvent(event)
    
    def mouseReleaseEvent(self, event: QMouseEvent) -> None:
        """
        重写鼠标释放
        :return None
        """
        if event.button() == Qt.MouseButton.LeftButton:
            event.accept()
            # 释放mention action, 重启 standbyAction
            self.actionEventQThread.standbyEvent()
            self.current_action = "Standby"
            self.actionChanged.emit(self.current_action)
            self.isFreeWalking = False
        return super().mouseReleaseEvent(event)

    def enterEvent(self, event):
        """鼠标进入窗口事件"""
        self.is_hovering = True
        if self.auto_hide:
            self.show_window_animation(True)
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开窗口事件"""
        self.is_hovering = False
        if self.auto_hide:
            self.show_window_animation(False)
        super().leaveEvent(event)

    def show_window_animation(self, show: bool):
        """窗口显示/隐藏动画"""
        opacity_effect = QGraphicsOpacityEffect(self)
        self.setGraphicsEffect(opacity_effect)
        
        animation = QPropertyAnimation(opacity_effect, b"opacity")
        animation.setDuration(300)
        animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        
        if show:
            animation.setStartValue(0.3)
            animation.setEndValue(1.0)
        else:
            animation.setStartValue(1.0)
            animation.setEndValue(0.3)
        
        animation.start()

    def closeEvent(self, event) -> None:
        """重写closeEvent"""
        # 播放退出语音
        self.hide()
        if self.isPlayVoiceOnClose is True:
            self.playFireflyVoice("VoiceOnClose")
        if self.fireflyVoicePackThread:
            self.fireflyVoicePackThread.exec_()

        try:
            self.stopTimer()
            # 停止自动动作定时器
            if self.auto_action_timer:
                self.auto_action_timer.stop()
            # 判断当前action是否存在，进行释放
            if self.actionEventQThread:
                self.actionEventQThread.requestInterruption = True
                self.actionEventQThread.exit()
                self.actionEventQThread.wait()
        except Exception as e:
            logger.error(e)
        finally:
            return super().closeEvent(event)
    
    def showEvent(self, event) -> None:
        # 播放启动语音
        try:
            if self.isPlayVoiceOnStart is True:
                self.playFireflyVoice("VoiceOnStart")
        except Exception as e:
            logger.error(e)
        finally:
            return super().showEvent(event)

    def playFireflyVoice(self, key: str) -> None:
        """
        通过`key`值生成播放指定语音的线程
        :param key: str | 需要播放语音的key
        """
        # 清理旧的语音包线程
        if self.fireflyVoicePackThread and self.fireflyVoicePackThread.isRunning():
            try:
                self.fireflyVoicePackThread.quit()
                self.fireflyVoicePackThread.wait(1000)  # 等待1秒
                if self.fireflyVoicePackThread.isRunning():
                    self.fireflyVoicePackThread.terminate()
                    self.fireflyVoicePackThread.wait(1000)
            except Exception as e:
                logger.error(f"清理旧语音包线程失败: {e}")
        
        self.fireflyVoicePackThread = FireflyVoicePackQThread(key)
        self.fireflyVoicePackThread.started.connect(self.VoicePackStartedCallback)
        self.fireflyVoicePackThread.start()

    def VoicePackStartedCallback(self, result: dict) -> None:
        """
        语音包播放后，获取`result`，并生成弹窗`PopupInterface`
        :param result: dict | 返回值
        """
        img = result.get('img')
        if img is None:
            img = DEF_IMG
        self.popupFace = PopupInterface(
            result['title'],
            img
        )
        self.popupFace.show()
    
    def switchBackground(self, filePath: str) -> None:
        """
        根据`filePath`值，切换背景图片
        :param filePath: str | 文件路径
        """
        try:
            if not os.path.exists(filePath):
                logger.error(f"背景图片文件不存在: {filePath}")
                return

            pixmap = QPixmap(filePath)
            if pixmap.isNull():
                logger.error(f"无法加载背景图片: {filePath}")
                return

            if self.scaling > 0:
                new_width = max(1, pixmap.width() // self.scaling)
                new_height = max(1, pixmap.height() // self.scaling)
                pixmap = pixmap.scaled(
                    new_width,
                    new_height,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )

            self.label.setPixmap(pixmap)
            self.label.resize(pixmap.size())
            self.resize(pixmap.size())
            self.currentBgImage = filePath

        except Exception as e:
            logger.error(f"切换背景图片时出错: {e}")

    def ActionEventMethod(self, result: str) -> None:
        """
        动作事件的返回方法
        :param result: bool | 判断是否正常结束
        """
        if not result:
            return None
        if result != "Standby" and self.isFreeWalking is False:
            self.actionEventQThread.standbyEvent()
            self.current_action = "Standby"
            self.actionChanged.emit(self.current_action)

        if self.isFreeWalking is True:
            self.actionEventQThread.load(self.walkingDirection)
            if self.walkingDirection == "left":
                moveFunc = self.FireflyMove("left")
            elif self.walkingDirection == "right":
                moveFunc = self.FireflyMove("right")
            moveFunc(self)
            # 检查是否到达屏幕边缘并改变方向
            if self.isFreeWalking:
                screen_geometry = self.app.primaryScreen().geometry()
                if (self.walkingDirection == "left" and self.x() <= screen_geometry.x()) or \
                   (self.walkingDirection == "right" and self.x() + self.width() >= screen_geometry.right()):
                    self.walkingDirection = "right" if self.walkingDirection == "left" else "left"
                    logger.info(f"Reached screen edge, turning to {self.walkingDirection}")
            
    def setFreeWalking(self) -> None:
        """设置为自由行动状态"""
        try:
            logger.info(f"当前游动状态: {self.isFreeWalking}")

            # 检查必要的属性
            if not hasattr(self, 'actionEventQThread'):
                logger.error("actionEventQThread 未初始化")
                return

            if not hasattr(self, 'walkingDirection'):
                logger.error("walkingDirection 未初始化")
                self.walkingDirection = "right"  # 设置默认值

            # 切换游动状态
            self.isFreeWalking = not self.isFreeWalking
            logger.info(f"游动状态已切换为: {self.isFreeWalking}")

            # 加载对应的动作
            if self.isFreeWalking:
                logger.info(f"开始游动，方向: {self.walkingDirection}")
                self.actionEventQThread.load(self.walkingDirection)
                self.current_action = self.walkingDirection
            else:
                logger.info("停止游动，切换到待机状态")
                self.actionEventQThread.load("Standby")
                self.current_action = "Standby"
            
            self.actionChanged.emit(self.current_action)

        except Exception as e:
            logger.error(f"设置游动状态失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")

    def FireflyMove(self, direction: str):
        """
        向指定方向移动`左右`
        :param direction: str | 方向， left or right
        :return callable
        """
        def left(self):
            x = self.x()
            # 窗口向左移动，直到撞到屏幕左边界
            if x > 0:
                self.move(x - 15, self.y())

        def right(self):
            # 获取当前窗口位置和屏幕的尺寸
            x = self.x()
            screen = QApplication.primaryScreen().geometry()
            # 窗口向右移动，直到撞到屏幕右边界
            if x + self.width() < screen.width():
                self.move(x + 15, self.y())
        return left if direction == "left" else right

    def startTimer(self) -> None:
        """启动执行动作的定时器"""
        if not hasattr(self, 'action_timer'):
            self.action_timer = QTimer(self)
            self.action_timer.timeout.connect(self.actionEventQThread.playNextImage)
        self.action_timer.start(200)

    def stopTimer(self) -> None:
        """停止执行动作的定时器"""
        if hasattr(self, 'action_timer'):
            self.action_timer.stop()

    def updateConfig(self) -> None:
        """更新当前窗口的配置"""
        self.mainConfigFileObject = ConfigFile()
        self.mainConfigFileObject.data = self.mainConfigFileObject.read()
        # 更新数据
        self.scaling = self.mainConfigFileObject.data.get("scaling", 0)
        self.isPlayVoiceOnStart = self.mainConfigFileObject.data.get("is_play_VoiceOnStart", True)
        self.isPlayVoiceOnClose = self.mainConfigFileObject.data.get("is_play_VoiceOnClose", True)
        
        # 更新新增配置项
        self.always_top = self.mainConfigFileObject.data.get("always_top", True)
        self.auto_hide = self.mainConfigFileObject.data.get("auto_hide", False)
        self.action_interval = self.mainConfigFileObject.data.get("action_interval", 30)
        self.auto_action = self.mainConfigFileObject.data.get("auto_action", True)
        
        # 更新窗口属性
        flags = Qt.WindowType.FramelessWindowHint | Qt.WindowType.Tool
        if self.always_top:
            flags |= Qt.WindowType.WindowStaysOnTopHint
        self.setWindowFlags(flags)
        
        # 更新自动动作系统
        if self.auto_action_timer:
            if self.auto_action:
                self.auto_action_timer.start(self.action_interval * 1000)
            else:
                self.auto_action_timer.stop()
        elif self.auto_action:
            self.init_auto_action_system()

    def get_current_action(self) -> str:
        """获取当前动作"""
        return self.current_action

    def get_status_info(self) -> dict:
        """获取状态信息"""
        return {
            "current_action": self.current_action,
            "is_free_walking": self.isFreeWalking,
            "walking_direction": self.walkingDirection,
            "is_hovering": self.is_hovering,
            "last_interaction": time.time() - self.last_interaction_time
        }
