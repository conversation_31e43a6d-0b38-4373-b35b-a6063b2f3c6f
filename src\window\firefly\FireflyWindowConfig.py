import os
import json
from typing import Union, Any
from loguru import logger
from src.config import config_manager


class ConfigFile:
    """
    主配置文件管理器（兼容旧接口）
    使用新的统一配置管理器
    """
    def __init__(self) -> None:
        self.data = self.read()

    def read(self) -> Union[dict, Any]:
        """
        读取主配置文件中的json数据
        :return Union[dict, Any]
        """
        try:
            return config_manager.get_config("main")
        except Exception as e:
            logger.error(f"读取主配置文件失败: {e}")
            return {}

    def write(self) -> bool:
        """
        写入数据到配置文件
        :return bool
        """
        try:
            return config_manager.set_config("main", self.data)
        except Exception as e:
            logger.error(f"写入主配置文件失败: {e}")
            return False

    def set(self, key: str, value: Union[str, bool, int, dict]) -> None:
        """
        根据`key`值对配置文件内容修改。
        Params:
            key: str                | 需要匹配的关键字
            value: Union[str, bool, int, dict] | 修改后的数据
        Returns:
            None
        """
        try:
            # 更新本地数据
            self.data[key] = value
            # 使用配置管理器保存
            config_manager.set_value("main", key, value)
        except Exception as e:
            logger.error(f"设置配置值失败 {key}: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        :param key: 配置键
        :param default: 默认值
        :return: 配置值
        """
        return config_manager.get_value("main", key, default)

    def reload(self) -> None:
        """重新加载配置"""
        self.data = config_manager.reload_config("main")
