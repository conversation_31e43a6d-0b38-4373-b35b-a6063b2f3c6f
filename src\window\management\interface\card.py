from qfluentwidgets import (
    CardWidget,
    IconWidget,
    BodyLabel,
    CaptionLabel,
    PushButton,
    TransparentToolButton,
    FluentIcon
)
from PySide6.QtWidgets import QWidget, QHBoxLayout, QVBoxLayout
from PySide6.QtGui import QColor


class AppCard(CardWidget):

    def __init__(self, icon, title, content, parent=None):
        super().__init__(parent)
        self.iconWidget = IconWidget(icon)
        self.titleLabel = BodyLabel(title, self)
        self.contentLabel = CaptionLabel(content, self)
        self.openButton = PushButton("open", self)
        self.moreButton = TransparentToolButton(FluentIcon.MORE, self)

        # 主布局
        mainLayout = QVBoxLayout()
        hBoxLayout = QHBoxLayout()
        mainLayout.addLayout(hBoxLayout)

        self.setFixedHeight(73)
        self.iconWidget.setFixedSize(48, 48)
        self.contentLabel.setTextColor("#606060", "#d2d2d2")  # linter误报，QFluentWidgets支持字符串色值
        self.openButton.setFixedWidth(120)

        hBoxLayout.setContentsMargins(20, 11, 11, 11)
        hBoxLayout.setSpacing(15)
        hBoxLayout.addWidget(self.iconWidget)

        vBoxLayout = QVBoxLayout()
        vBoxLayout.setContentsMargins(0, 0, 0, 0)
        vBoxLayout.setSpacing(0)
        vBoxLayout.addWidget(self.titleLabel)
        vBoxLayout.addWidget(self.contentLabel)
        hBoxLayout.addLayout(vBoxLayout)

        hBoxLayout.addStretch(1)
        hBoxLayout.addWidget(self.openButton)
        hBoxLayout.addWidget(self.moreButton)
        self.moreButton.setFixedSize(32, 32)
        self.setLayout(mainLayout)  # linter误报，QVBoxLayout继承自QLayout
