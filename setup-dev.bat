@echo off
echo ========================================
echo MyFlowingFireflyWife 开发环境设置
echo ========================================

echo 检查 Python 环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到 Python，请确保 Python 已安装并添加到 PATH
    pause
    exit /b 1
)

echo 创建虚拟环境...
if exist "venv" (
    echo 虚拟环境已存在，跳过创建
) else (
    python -m venv venv
    if %errorlevel% neq 0 (
        echo 创建虚拟环境失败！
        pause
        exit /b 1
    )
)

echo 激活虚拟环境...
call venv\Scripts\activate.bat

echo 升级 pip...
python -m pip install --upgrade pip

echo 安装依赖...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 安装依赖失败！
    pause
    exit /b 1
)

echo 创建必要的目录...
if not exist "data\config" mkdir "data\config"
if not exist "data\assets" mkdir "data\assets"
if not exist "data\audio" mkdir "data\audio"
if not exist "log" mkdir "log"

echo ========================================
echo 开发环境设置完成！
echo 
echo 使用方法:
echo 1. 激活虚拟环境: venv\Scripts\activate.bat
echo 2. 运行应用: python MyFlowingFireflyWife.py
echo 3. 退出虚拟环境: deactivate
echo ========================================
pause
