#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MyFlowingFireflyWife 启动脚本
用于快速启动和测试应用
"""
import sys
import os
from loguru import logger

def setup_environment():
    """设置运行环境"""
    # 添加项目根目录到Python路径
    project_root = os.path.dirname(os.path.abspath(__file__))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    # 设置工作目录
    os.chdir(project_root)
    
    # 创建必要的目录
    directories = [
        "log",
        "data/config/extends",
        "data/config/extends/BatteryVoice"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def check_dependencies():
    """检查依赖"""
    required_modules = [
        'PySide6',
        'loguru',
        'pyaudio',
        'PIL',
        'numpy'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        logger.error(f"缺少必要的依赖模块: {', '.join(missing_modules)}")
        logger.info("请运行: pip install -r requirements.txt")
        return False
    
    return True

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("MyFlowingFireflyWife 启动脚本")
    logger.info("=" * 50)
    
    # 设置环境
    setup_environment()
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 配置日志
    logger.add("log/startup.log", rotation="10 MB", level="DEBUG")
    
    try:
        # 导入并运行主程序
        from MyFlowingFireflyWife import main as app_main
        return app_main()
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return 1

if __name__ == '__main__':
    sys.exit(main()) 