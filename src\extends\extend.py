# -*- coding: utf-8 -*-
"""
扩展功能管理器
提供扩展功能的加载、管理和控制
"""
import os
import json
import importlib
from typing import Dict, List, Optional, Any
from loguru import logger

extendsDir = os.path.join(os.getcwd(), "data", "config", "extends")


class ExtendInfoType:
    """扩展信息类型"""
    def __init__(self, name: str, description: str = "", version: str = "1.0.0"):
        self.name = name
        self.description = description
        self.version = version
        self.isStatic = False
        self.enabled = False
        self.config = {}


class ExtendType:
    """扩展类型管理器"""

    @staticmethod
    def readInfoJson(extendName: str) -> ExtendInfoType:
        """
        读取扩展信息
        :param extendName: 扩展名称
        :return: 扩展信息对象
        """
        try:
            info_path = os.path.join(
                os.getcwd(), "data", "config", "extends", 
                extendName, "info.json"
            )
            
            if os.path.exists(info_path):
                with open(info_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                info = ExtendInfoType(
                    name=data.get('name', extendName),
                    description=data.get('description', ''),
                    version=data.get('version', '1.0.0')
                )
                info.isStatic = data.get('isStatic', False)
                info.enabled = data.get('enabled', False)
                info.config = data.get('config', {})
                
                return info
            else:
                # 创建默认信息
                default_info = ExtendInfoType(extendName)
                ExtendType.writeInfoJson(extendName, default_info)
                return default_info
                
        except Exception as e:
            logger.error(f"读取扩展信息失败 {extendName}: {e}")
            return ExtendInfoType(extendName)

    @staticmethod
    def writeInfoJson(extendName: str, info: ExtendInfoType) -> bool:
        """
        写入扩展信息
        :param extendName: 扩展名称
        :param info: 扩展信息对象
        :return: 是否成功
        """
        try:
            extend_dir = os.path.join(
                os.getcwd(), "data", "config", "extends", extendName
            )
            os.makedirs(extend_dir, exist_ok=True)
            
            info_path = os.path.join(extend_dir, "info.json")
            
            data = {
                'name': info.name,
                'description': info.description,
                'version': info.version,
                'isStatic': info.isStatic,
                'enabled': info.enabled,
                'config': info.config
            }
            
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            logger.error(f"写入扩展信息失败 {extendName}: {e}")
            return False

    @staticmethod
    def loadExtend(extendName: str) -> Optional[Any]:
        """
        加载扩展模块
        :param extendName: 扩展名称
        :return: 扩展模块对象
        """
        try:
            module_path = f"src.extends.{extendName}.main"
            module = importlib.import_module(module_path)
            
            # 查找扩展类
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if hasattr(attr, '__class__') and 'Extend' in attr.__class__.__name__:
                    return attr
            
            logger.warning(f"扩展 {extendName} 中未找到扩展类")
            return None
            
        except ImportError as e:
            logger.error(f"导入扩展模块失败 {extendName}: {e}")
            return None
        except Exception as e:
            logger.error(f"加载扩展失败 {extendName}: {e}")
            return None

    @staticmethod
    def getAvailableExtends() -> List[str]:
        """
        获取可用的扩展列表
        :return: 扩展名称列表
        """
        extends = []
        extends_dir = os.path.join(os.getcwd(), "data", "config", "extends")
        
        if os.path.exists(extends_dir):
            for item in os.listdir(extends_dir):
                info_path = os.path.join(extends_dir, item, "info.json")
                if os.path.exists(info_path):
                    extends.append(item)
        
        return extends
    
    @staticmethod
    def isExtendEnabled(extendName: str) -> bool:
        """
        检查扩展是否启用
        :param extendName: 扩展名称
        :return: 是否启用
        """
        info = ExtendType.readInfoJson(extendName)
        return info.enabled
    
    @staticmethod
    def enableExtend(extendName: str) -> bool:
        """
        启用扩展
        :param extendName: 扩展名称
        :return: 是否成功
        """
        try:
            info = ExtendType.readInfoJson(extendName)
            info.enabled = True
            return ExtendType.writeInfoJson(extendName, info)
        except Exception as e:
            logger.error(f"启用扩展失败 {extendName}: {e}")
            return False
    
    @staticmethod
    def disableExtend(extendName: str) -> bool:
        """
        禁用扩展
        :param extendName: 扩展名称
        :return: 是否成功
        """
        try:
            info = ExtendType.readInfoJson(extendName)
            info.enabled = False
            return ExtendType.writeInfoJson(extendName, info)
        except Exception as e:
            logger.error(f"禁用扩展失败 {extendName}: {e}")
            return False


class ExtendManager:
    """扩展管理器"""
    
    def __init__(self):
        self.extends: Dict[str, Any] = {}
        self.running_extends: Dict[str, Any] = {}
    
    def loadAllExtends(self) -> None:
        """加载所有启用的扩展"""
        available_extends = ExtendType.getAvailableExtends()
        
        for extend_name in available_extends:
            if ExtendType.isExtendEnabled(extend_name):
                self.loadExtend(extend_name)
    
    def loadExtend(self, extendName: str) -> bool:
        """
        加载指定扩展
        :param extendName: 扩展名称
        :return: 是否成功
        """
        try:
            extend_obj = ExtendType.loadExtend(extendName)
            if extend_obj:
                self.extends[extendName] = extend_obj
                logger.info(f"扩展 {extendName} 加载成功")
                return True
            else:
                logger.error(f"扩展 {extendName} 加载失败")
                return False
        except Exception as e:
            logger.error(f"加载扩展失败 {extendName}: {e}")
            return False
    
    def startExtend(self, extendName: str) -> bool:
        """
        启动扩展
        :param extendName: 扩展名称
        :return: 是否成功
        """
        try:
            if extendName not in self.extends:
                if not self.loadExtend(extendName):
                    return False
            
            extend_obj = self.extends[extendName]
            if hasattr(extend_obj, 'start'):
                if extend_obj.start():
                    self.running_extends[extendName] = extend_obj
                    logger.info(f"扩展 {extendName} 启动成功")
                    return True
                else:
                    logger.error(f"扩展 {extendName} 启动失败")
                    return False
            else:
                logger.error(f"扩展 {extendName} 没有 start 方法")
                return False
                
        except Exception as e:
            logger.error(f"启动扩展失败 {extendName}: {e}")
            return False
    
    def stopExtend(self, extendName: str) -> bool:
        """
        停止扩展
        :param extendName: 扩展名称
        :return: 是否成功
        """
        try:
            if extendName in self.running_extends:
                extend_obj = self.running_extends[extendName]
                if hasattr(extend_obj, 'stop'):
                    if extend_obj.stop():
                        del self.running_extends[extendName]
                        logger.info(f"扩展 {extendName} 停止成功")
                        return True
                    else:
                        logger.error(f"扩展 {extendName} 停止失败")
                        return False
                else:
                    logger.error(f"扩展 {extendName} 没有 stop 方法")
                    return False
            else:
                logger.warning(f"扩展 {extendName} 未在运行")
                return True
                
        except Exception as e:
            logger.error(f"停止扩展失败 {extendName}: {e}")
            return False
    
    def stopAllExtends(self) -> None:
        """停止所有运行的扩展"""
        for extend_name in list(self.running_extends.keys()):
            self.stopExtend(extend_name)
    
    def getExtendInfo(self, extendName: str) -> Optional[ExtendInfoType]:
        """
        获取扩展信息
        :param extendName: 扩展名称
        :return: 扩展信息对象
        """
        return ExtendType.readInfoJson(extendName)
    
    def getRunningExtends(self) -> List[str]:
        """
        获取正在运行的扩展列表
        :return: 扩展名称列表
        """
        return list(self.running_extends.keys())
    
    def getLoadedExtends(self) -> List[str]:
        """
        获取已加载的扩展列表
        :return: 扩展名称列表
        """
        return list(self.extends.keys())


# 全局扩展管理器实例
_extend_manager = None

def get_extend_manager() -> ExtendManager:
    """获取全局扩展管理器实例"""
    global _extend_manager
    if _extend_manager is None:
        _extend_manager = ExtendManager()
    return _extend_manager

