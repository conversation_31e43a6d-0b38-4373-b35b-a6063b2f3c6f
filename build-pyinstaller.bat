@echo off
echo ========================================
echo MyFlowingFireflyWife 构建脚本
echo ========================================

echo 检查 Python 环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到 Python，请确保 Python 已安装并添加到 PATH
    pause
    exit /b 1
)

echo 检查依赖...
pip show pyinstaller >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装 PyInstaller...
    pip install pyinstaller
)

echo 清理旧的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"

echo 开始构建...
pyinstaller ^
    --onefile ^
    --windowed ^
    --add-data "data;data" ^
    --add-data "src;src" ^
    --icon "data/assets/images/firefly/icon/icon.ico" ^
    --name "MyFlowingFireflyWife" ^
    --distpath "dist" ^
    --workpath "build" ^
    --specpath "." ^
    MyFlowingFireflyWife.py

if %errorlevel% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo 构建完成！
echo 可执行文件位置: dist\MyFlowingFireflyWife.exe
echo 正在复制必要文件...

if not exist "dist\data" mkdir "dist\data"
xcopy "data" "dist\data" /E /I /Y

echo ========================================
echo 构建成功完成！
echo 可执行文件: dist\MyFlowingFireflyWife.exe
echo ========================================
pause