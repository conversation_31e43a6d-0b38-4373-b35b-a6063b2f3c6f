from qfluentwidgets import (
    Action,
    FluentIcon,
    RoundMenu,
    MenuAnimationType
)
from PySide6.QtGui import QActionGroup, QAction
from PySide6.QtWidgets import QMenu
from loguru import logger
import traceback


class Menu:
    def __init__(self, parent=None) -> None:
        self.parent = parent
        self.management_window = None  # 缓存管理窗口

        # 使用标准 QAction 创建菜单项，避免 qfluentwidgets 的兼容性问题
        self.manageAction = QAction("管理", self.parent)
        self.manageAction.triggered.connect(self._safe_show_management)

        self.freeWalkingAction = QAction("游动", self.parent)
        self.freeWalkingAction.triggered.connect(self._safe_toggle_walking)

        self.feedingAction = QAction("喂食", self.parent)
        self.feedingAction.triggered.connect(self._safe_feeding)

        self.sleepAction = QAction("睡觉", self.parent)
        self.sleepAction.triggered.connect(self._safe_sleep)

        self.exitAction = QAction("退出", self.parent)
        self.exitAction.triggered.connect(self._safe_exit)

        # 新增语音相关菜单项
        self.voice_daily_action = QAction("日常语音", self.parent)
        self.voice_daily_action.triggered.connect(self._safe_play_daily_voice)

        self.voice_interaction_action = QAction("交互语音", self.parent)
        self.voice_interaction_action.triggered.connect(self._safe_play_interaction_voice)

        self.voice_random_action = QAction("随机语音", self.parent)
        self.voice_random_action.triggered.connect(self._safe_play_random_voice)

    def _safe_show_management(self):
        """安全地显示管理窗口"""
        try:
            logger.info("尝试打开管理窗口...")

            # 检查必要的配置文件
            logger.debug("检查配置文件...")
            import os
            config_files = [
                "data/config/live2d.config.json",
                "data/config/main.json"
            ]

            for config_file in config_files:
                if not os.path.exists(config_file):
                    logger.warning(f"配置文件不存在: {config_file}")
                    # 创建默认配置文件
                    os.makedirs(os.path.dirname(config_file), exist_ok=True)
                    if "live2d" in config_file:
                        default_config = {
                            "current-model": "firefly",
                            "enabled": False,
                            "scale": 1.0
                        }
                    else:
                        default_config = {
                            "scaling": 0,
                            "is_play_VoiceOnStart": False,
                            "is_play_VoiceOnClose": False
                        }

                    with open(config_file, 'w', encoding='utf-8') as f:
                        import json
                        json.dump(default_config, f, indent=4, ensure_ascii=False)
                    logger.info(f"已创建默认配置文件: {config_file}")

            # 延迟导入管理窗口模块
            logger.debug("延迟导入管理窗口模块...")
            from src.window.management.ManagementWindow import ManagementWindow
            logger.debug("管理窗口模块导入成功")

            if self.management_window is None or not self.management_window.isVisible():
                logger.info("创建新的管理窗口")
                logger.debug("步骤1: 准备创建管理窗口实例")

                # 检查 QApplication 状态
                from PySide6.QtWidgets import QApplication
                app = QApplication.instance()
                if app is None:
                    logger.error("QApplication 实例不存在，无法创建管理窗口")
                    return
                logger.debug("步骤2: QApplication 检查通过")

                logger.debug("步骤3: 调用管理窗口构造函数")
                self.management_window = ManagementWindow(self.parent)
                logger.debug("步骤4: 管理窗口创建成功")

            logger.debug("显示管理窗口")
            self.management_window.show()
            self.management_window.raise_()
            self.management_window.activateWindow()
            logger.info("管理窗口打开成功")

        except ImportError as import_error:
            logger.error(f"管理窗口模块导入失败: {import_error}")
            logger.error(f"导入错误详情: {traceback.format_exc()}")
        except Exception as e:
            logger.error(f"打开管理窗口失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")

    def _safe_toggle_walking(self):
        """安全地切换游动模式"""
        try:
            logger.info("尝试切换游动模式...")
            if self.parent is not None and hasattr(self.parent, 'setFreeWalking'):
                self.parent.setFreeWalking()
                logger.info("游动模式切换成功")
            else:
                logger.error("父窗口没有 setFreeWalking 方法")
        except Exception as e:
            logger.error(f"切换游动模式失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")

    def _safe_feeding(self):
        """安全地执行喂食动作"""
        try:
            logger.info("尝试执行喂食动作...")
            if (self.parent is not None and 
                hasattr(self.parent, 'actionEventQThread') and 
                hasattr(self.parent.actionEventQThread, 'eatEvent')):
                self.parent.actionEventQThread.eatEvent()
                logger.info("喂食动作执行成功")
            else:
                logger.error("无法找到 eatEvent 方法")
        except Exception as e:
            logger.error(f"执行喂食动作失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")

    def _safe_sleep(self):
        """安全地执行睡觉动作"""
        try:
            logger.info("尝试执行睡觉动作...")
            if (self.parent is not None and 
                hasattr(self.parent, 'actionEventQThread') and 
                hasattr(self.parent.actionEventQThread, 'sleepEvent')):
                self.parent.actionEventQThread.sleepEvent()
                logger.info("睡觉动作执行成功")
            else:
                logger.error("无法找到 sleepEvent 方法")
        except Exception as e:
            logger.error(f"执行睡觉动作失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")

    def _safe_exit(self):
        """安全地退出应用"""
        try:
            logger.info("尝试退出应用...")
            if self.parent is not None and hasattr(self.parent, 'close'):
                self.parent.close()
                logger.info("应用退出成功")
            else:
                logger.error("父窗口没有 close 方法")
        except Exception as e:
            logger.error(f"退出应用失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")

    def _safe_play_daily_voice(self):
        """安全地播放日常语音"""
        try:
            logger.info("尝试播放日常语音...")
            if self.parent is not None and hasattr(self.parent, 'playFireflyVoice'):
                self.parent.playFireflyVoice("Daily")
                logger.info("日常语音播放成功")
            else:
                logger.error("父窗口没有 playFireflyVoice 方法")
        except Exception as e:
            logger.error(f"播放日常语音失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")

    def _safe_play_interaction_voice(self):
        """安全地播放交互语音"""
        try:
            logger.info("尝试播放交互语音...")
            if self.parent is not None and hasattr(self.parent, 'playFireflyVoice'):
                self.parent.playFireflyVoice("Interaction")
                logger.info("交互语音播放成功")
            else:
                logger.error("父窗口没有 playFireflyVoice 方法")
        except Exception as e:
            logger.error(f"播放交互语音失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")

    def _safe_play_random_voice(self):
        """安全地播放随机语音"""
        try:
            logger.info("尝试播放随机语音...")
            if self.parent is not None and hasattr(self.parent, 'play_random_voice'):
                import random
                voice_types = ["Daily", "Interaction"]
                voice_type = random.choice(voice_types)
                self.parent.play_random_voice(voice_type)
                logger.info(f"随机语音播放成功: {voice_type}")
            else:
                logger.error("父窗口没有 play_random_voice 方法")
        except Exception as e:
            logger.error(f"播放随机语音失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
        
    def contextMenuEvent(self, e):
        """安全地显示右键菜单"""
        try:
            logger.info("尝试显示右键菜单...")

            # 检查 QApplication 是否存在
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app is None:
                logger.error("QApplication 实例不存在！")
                return
            logger.debug("QApplication 实例检查通过")

            # 检查 qfluentwidgets 模块状态
            logger.debug("检查 qfluentwidgets 模块状态...")
            try:
                import qfluentwidgets
                logger.debug(f"qfluentwidgets 版本: {getattr(qfluentwidgets, '__version__', '未知')}")
            except Exception as qfw_error:
                logger.error(f"qfluentwidgets 模块检查失败: {qfw_error}")

            # 由于 RoundMenu 在构造函数中有问题，直接使用标准 QMenu
            logger.debug("由于 RoundMenu 兼容性问题，直接使用标准 QMenu")
            try:
                logger.debug("创建标准 QMenu...")
                self.menu = QMenu()
                logger.debug("标准 QMenu 创建成功")
                # use_round_menu = False  # 不需要这个变量了
            except Exception as qmenu_error:
                logger.error(f"标准 QMenu 创建失败: {qmenu_error}")
                logger.error(f"QMenu 错误详情: {traceback.format_exc()}")
                return  # 完全失败，退出

            # 添加菜单项
            logger.debug("添加管理菜单项")
            self.menu.addAction(self.manageAction)
            self.menu.addSeparator()

            logger.debug("添加功能菜单项")
            self.menu.addAction(self.freeWalkingAction)
            self.menu.addAction(self.sleepAction)
            self.menu.addAction(self.feedingAction)
            self.menu.addSeparator()

            logger.debug("添加语音菜单项")
            self.menu.addAction(self.voice_daily_action)
            self.menu.addAction(self.voice_interaction_action)
            self.menu.addAction(self.voice_random_action)
            self.menu.addSeparator()

            logger.debug("添加退出菜单项")
            self.menu.addAction(self.exitAction)

            logger.debug("显示菜单")
            # 使用标准 QMenu 的 exec 方法
            self.menu.exec(e.globalPos())
            logger.info("右键菜单显示成功")
        except Exception as e:
            logger.error(f"显示右键菜单失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
