# MyFlowingFireflyWife

![icon](data/assets/images/firefly/default/bg.png)

## 项目简介

MyFlowingFireflyWife 是一个使用 Python 开发的桌面宠物应用，灵感来源于星穹铁道中的角色“流萤”。该应用旨在为用户提供一个可爱的虚拟角色，增添桌面的趣味性和活力。

## 功能特性

- 🎭 **丰富的动作系统** - 支持待机、移动、睡觉、吃饭、摸头等多种动作
- 🔊 **智能语音交互** - 根据时间段播放不同的语音内容
- 🖱️ **直观的用户交互** - 支持鼠标拖拽、点击触发不同动作
- 🎨 **Live2D 支持** - 支持 Live2D 模型显示（可选）
- ⚙️ **灵活的配置系统** - 支持自定义缩放、语音开关等设置
- 🔋 **系统扩展功能** - 电池语音提醒等实用功能
- 🪟 **无边框透明窗口** - 始终置顶，不影响其他应用使用

## 系统要求

- **操作系统**: Windows 10/11
- **Python**: 3.8+
- **内存**: 至少 512MB 可用内存
- **存储**: 至少 100MB 可用空间

## 安装说明

### 方式一：直接运行（推荐）

1. 确保已安装 Python 3.8+
2. 克隆或下载项目到本地
3. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
4. 运行应用：
   ```bash
   python MyFlowingFireflyWife.py
   ```

### 方式二：打包运行

1. 使用 PyInstaller 打包：
   ```bash
   # Windows
   build-pyinstaller.bat

   # 或手动执行
   pyinstaller --onefile --add-data "src/*;." MyFlowingFireflyWife.py
   ```
2. 运行生成的可执行文件

## 使用说明

### 基本操作

- **拖拽移动**: 按住鼠标左键拖拽窗口
- **摸头互动**: 点击角色上半部分触发摸头动作
- **提起动作**: 点击角色下半部分触发提起动作
- **右键菜单**: 右键点击打开功能菜单

### 菜单功能

- **管理**: 打开设置和管理界面
- **游动**: 启用/禁用自由移动模式
- **喂食**: 触发吃饭动作
- **睡觉**: 触发睡觉动作
- **退出**: 关闭应用

### 配置设置

在管理界面中可以配置：

- **缩放比例**: 调整角色显示大小
- **语音设置**: 开启/关闭启动和退出语音
- **Live2D 设置**: 配置 Live2D 模型（如果可用）
- **扩展功能**: 启用/禁用各种扩展功能

## 项目结构

```
MyFlowingFireflyWife/
├── MyFlowingFireflyWife.py     # 主程序入口
├── requirements.txt            # 依赖列表
├── build-pyinstaller.bat      # 构建脚本
├── data/                       # 数据目录
│   ├── assets/                 # 资源文件
│   ├── audio/                  # 音频文件
│   ├── config/                 # 配置文件
│   └── live2d/                 # Live2D 模型
├── src/                        # 源代码
│   ├── config/                 # 配置管理
│   ├── window/                 # 窗口管理
│   ├── ActionEvent/            # 动作事件系统
│   ├── FireflyVoicePack/       # 语音包管理
│   ├── MediaPlayer/            # 媒体播放器
│   └── extends/                # 扩展功能
└── log/                        # 日志文件
```

## 开发说明

### 技术栈

- **GUI 框架**: PySide6
- **UI 组件**: PySide6-Fluent-Widgets
- **音频处理**: PyAudio
- **图像处理**: Pillow
- **日志系统**: loguru
- **Web 引擎**: QWebEngine (Live2D 支持)

### 添加新动作

1. 在 `data/config/action_pictures.json` 中添加动作配置
2. 在 `src/ActionEvent/Actions.py` 中添加对应的方法
3. 将动作图片放置在指定目录

### 添加新语音

1. 在 `data/config/voicepack.json` 中添加语音配置
2. 将音频文件放置在 `data/audio/` 目录
3. 配置不同时间段的语音内容

## 故障排除

### 常见问题

1. **应用无法启动**
   - 检查 Python 版本是否为 3.8+
   - 确认所有依赖已正确安装
   - 查看 `log/latest.log` 获取详细错误信息

2. **音频无法播放**
   - 确认系统音频设备正常工作
   - 检查音频文件是否存在且格式正确
   - 确认 PyAudio 已正确安装

3. **Live2D 无法显示**
   - 确认 Live2D 模型文件完整
   - 检查 Web 引擎组件是否正常
   - 查看浏览器控制台错误信息

### 日志文件

应用运行日志保存在 `log/latest.log`，包含详细的运行信息和错误记录。

## 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 GPL-3.0 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- 感谢星穹铁道提供的角色灵感
- 感谢所有贡献者和用户的支持

## 联系方式

如果您有任何问题或建议，请通过以下方式联系：

- 提交 GitHub Issue
- 发送邮件至项目维护者

---

MyFlowingFireflyWife 是一个可爱而有趣的桌面宠物应用，为您的电脑桌面增添了一份活力和乐趣。感谢您选择并使用 MyFlowingFireflyWife！
