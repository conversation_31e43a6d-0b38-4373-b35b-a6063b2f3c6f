# -*- coding: utf-8 -*-
"""
统一配置管理系统
提供配置文件的读取、写入、验证和缓存功能
"""
import os
import json
import threading
from typing import Any, Dict, Optional, Union
from pathlib import Path
from loguru import logger


class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        :param config_dir: 配置文件目录，默认为 data/config
        """
        if config_dir is None:
            config_dir = os.path.join(os.getcwd(), "data", "config")
        
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置缓存
        self._cache: Dict[str, Dict] = {}
        self._cache_lock = threading.RLock()
        
        # 默认配置
        self._defaults = {
            "main": {
                "scaling": 0,
                "currentBgImage": "data/assets/images/firefly/default/bg.png",
                "is_play_VoiceOnStart": False,
                "is_play_VoiceOnClose": False,
                "window_position": {"x": 100, "y": 100},
                "window_size": {"width": 300, "height": 400}
            },
            "live2d": {
                "current-model": "firefly",
                "enabled": False,
                "scale": 1.0
            },
            "action_pictures": {},
            "voicepack": {
                "VoiceOnStart": {
                    "morn": [],
                    "noon": [],
                    "evening": [],
                    "night": [],
                    "other": []
                },
                "VoiceOnClose": {
                    "morn": [],
                    "noon": [],
                    "evening": [],
                    "night": [],
                    "other": []
                }
            }
        }
    
    def _get_config_path(self, config_name: str) -> Path:
        """获取配置文件路径"""
        return self.config_dir / f"{config_name}.json"
    
    def _load_config(self, config_name: str) -> Dict:
        """从文件加载配置"""
        config_path = self._get_config_path(config_name)
        
        try:
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 合并默认配置
                    if config_name in self._defaults:
                        default_config = self._defaults[config_name].copy()
                        default_config.update(data)
                        return default_config
                    return data
            else:
                # 创建默认配置文件
                default_config = self._defaults.get(config_name, {})
                self._save_config(config_name, default_config)
                return default_config
                
        except (json.JSONDecodeError, IOError) as e:
            logger.error(f"加载配置文件失败 {config_path}: {e}")
            # 返回默认配置
            return self._defaults.get(config_name, {})
        except Exception as e:
            logger.error(f"加载配置文件时出现未知错误 {config_path}: {e}")
            return self._defaults.get(config_name, {})
    
    def _save_config(self, config_name: str, data: Dict) -> bool:
        """保存配置到文件"""
        config_path = self._get_config_path(config_name)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败 {config_path}: {e}")
            return False
    
    def get_config(self, config_name: str, use_cache: bool = True) -> Dict:
        """
        获取配置
        :param config_name: 配置名称
        :param use_cache: 是否使用缓存
        :return: 配置字典
        """
        with self._cache_lock:
            if use_cache and config_name in self._cache:
                return self._cache[config_name].copy()
            
            config = self._load_config(config_name)
            if use_cache:
                self._cache[config_name] = config.copy()
            return config
    
    def set_config(self, config_name: str, data: Dict, save_immediately: bool = True) -> bool:
        """
        设置配置
        :param config_name: 配置名称
        :param data: 配置数据
        :param save_immediately: 是否立即保存到文件
        :return: 是否成功
        """
        with self._cache_lock:
            self._cache[config_name] = data.copy()
            
            if save_immediately:
                return self._save_config(config_name, data)
            return True
    
    def get_value(self, config_name: str, key: str, default: Any = None) -> Any:
        """
        获取配置中的特定值
        :param config_name: 配置名称
        :param key: 键名，支持点号分隔的嵌套键
        :param default: 默认值
        :return: 配置值
        """
        config = self.get_config(config_name)
        
        # 支持嵌套键，如 "window.position.x"
        keys = key.split('.')
        value = config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_value(self, config_name: str, key: str, value: Any, save_immediately: bool = True) -> bool:
        """
        设置配置中的特定值
        :param config_name: 配置名称
        :param key: 键名，支持点号分隔的嵌套键
        :param value: 值
        :param save_immediately: 是否立即保存
        :return: 是否成功
        """
        config = self.get_config(config_name)
        
        # 支持嵌套键
        keys = key.split('.')
        current = config
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # 设置值
        current[keys[-1]] = value
        
        return self.set_config(config_name, config, save_immediately)
    
    def reload_config(self, config_name: str) -> Dict:
        """
        重新加载配置
        :param config_name: 配置名称
        :return: 配置字典
        """
        with self._cache_lock:
            if config_name in self._cache:
                del self._cache[config_name]
            return self.get_config(config_name)
    
    def save_all(self) -> bool:
        """保存所有缓存的配置"""
        with self._cache_lock:
            success = True
            for config_name, data in self._cache.items():
                if not self._save_config(config_name, data):
                    success = False
            return success
    
    def clear_cache(self) -> None:
        """清空缓存"""
        with self._cache_lock:
            self._cache.clear()


# 全局配置管理器实例（延迟初始化）
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

# 为了向后兼容，创建一个属性访问器
class ConfigManagerProxy:
    """配置管理器代理，实现延迟初始化"""
    def __getattr__(self, name):
        return getattr(get_config_manager(), name)

    def __call__(self, *args, **kwargs):
        return get_config_manager()(*args, **kwargs)

# 全局实例（延迟初始化）
config_manager = ConfigManagerProxy()
