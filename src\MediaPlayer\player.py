import wave
import pyaudio
from typing import Optional
from loguru import logger
from PySide6.QtCore import QThread, Signal


class AudioPlayer:
    def __init__(self, audioFilePath: str) -> None:
        self.audioFilePath = audioFilePath
        self.audio: Optional[wave.Wave_read] = None

    def load_audio(self) -> bool:
        """加载WAV文件并存储wave对象"""
        try:
            self.audio = wave.open(self.audioFilePath, 'rb')
            return True
        except (FileNotFoundError, wave.Error) as e:
            logger.error(f"Error loading audio file {self.audioFilePath}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error loading audio file {self.audioFilePath}: {e}")
            return False

    def play(self) -> bool:
        """播放音频"""
        if self.audio is None:
            if not self.load_audio():
                return False

        if self.audio is None:
            logger.error("Audio file not loaded")
            return False

        p = None
        stream = None
        try:
            # 初始化pyaudio
            p = pyaudio.PyAudio()

            # 打开流
            stream = p.open(
                format=p.get_format_from_width(self.audio.getsampwidth()),
                channels=self.audio.getnchannels(),
                rate=self.audio.getframerate(),
                output=True
            )

            # 读取数据并播放
            while True:
                data = self.audio.readframes(1024)
                if not data:
                    break
                stream.write(data)

            return True

        except Exception as e:
            logger.error(f"Error playing audio: {e}")
            return False
        finally:
            # 清理资源
            if stream:
                try:
                    stream.stop_stream()
                    stream.close()
                except Exception as e:
                    logger.error(f"Error closing stream: {e}")
            if self.audio:
                try:
                    self.audio.close()
                except Exception as e:
                    logger.error(f"Error closing audio: {e}")
                finally:
                    self.audio = None
            if p:
                try:
                    p.terminate()
                except Exception as e:
                    logger.error(f"Error terminating PyAudio: {e}")


class AudioPlayerQThread(QThread):
    """音频播放线程"""
    playbackFinished = Signal()
    playbackError = Signal(str)

    def __init__(self, audioFilePath: str):
        super().__init__(parent=None)
        self.audioFilePath = audioFilePath
        self.player = AudioPlayer(self.audioFilePath)

    def run(self) -> None:
        """重写 QThread 的 run 方法来播放音频"""
        try:
            success = self.player.play()
            if not success:
                self.playbackError.emit(f"Failed to play audio: {self.audioFilePath}")
        except Exception as e:
            logger.error(f"Error in audio playback thread: {e}")
            self.playbackError.emit(str(e))
        finally:
            # 播放完成后发出信号
            self.playbackFinished.emit()
