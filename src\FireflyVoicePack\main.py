import os
import json
import random
from datetime import datetime
from typing import Union, Optional
from loguru import logger
from PySide6.QtCore import QThread, Signal

from src.MediaPlayer import AudioPlayer

DATA_DIR = os.path.join(os.getcwd(), "data")

class VoicePack:
    @staticmethod
    def getAllVoicePack() -> dict:
        configFileDir = os.path.join(DATA_DIR, "config", "voicepack.json")
        try:
            with open(configFileDir, "r+", encoding="utf-8") as rfp:
                allPack = json.load(rfp)
            return allPack
        except (IOError, json.JSONDecodeError) as e:
            logger.error(f"无法读取或分析voicepack.json: {e}")
            return {}

    @staticmethod
    def getVoicePackByKey(key: str) -> Union[dict, None]:
        allVoicePack = VoicePack.getAllVoicePack()
        return allVoicePack.get(key)

    @staticmethod
    def getTimeOfDay() -> str:
        now = datetime.now()
        hour = now.hour
        if 6 <= hour < 8:
            return "morn"
        elif 10 <= hour < 12:
            return "noon"
        elif 18 <= hour < 21:
            return "evening"
        elif 21 <= hour < 24 or 0 <= hour < 6:
            return "night"
        else:
            return "other"

class FireflyVoicePackQThread(QThread):
    started = Signal(dict)
    played = Signal()

    def __init__(self, key: Optional[str] = None):
        super().__init__()
        self.key = key
        self.voicePackData: Optional[dict] = None

    def run(self) -> None:
        try:
            self.voicePackData = VoicePack.getVoicePackByKey(self.key)
            if not self.voicePackData:
                logger.error(f"未找到语音包数据: {self.key}")
                return

            timeOfDay = VoicePack.getTimeOfDay()
            timeData = self.voicePackData.get(timeOfDay, self.voicePackData.get('other'))

            if not timeData or not isinstance(timeData, list):
                logger.error(f"语音包数据格式错误: {self.key}, {timeOfDay}")
                return

            self.voicePackData = random.choice(timeData)

        except (IndexError, TypeError, AttributeError) as e:
            logger.error(f"处理语音包数据时出错: {e}")
            return
        except Exception as e:
            logger.error(f"语音包线程运行时出现未知错误: {e}")
            return

        if not self.voicePackData or not isinstance(self.voicePackData, dict):
            logger.error("语音包数据无效")
            return

        wav_path = self.voicePackData.get('wav')
        if not wav_path:
            logger.error("语音包数据中缺少音频文件路径")
            return

        self.started.emit(self.voicePackData)

        try:
            self.player = AudioPlayer(wav_path)
            if self.player.play():
                self.played.emit()
            else:
                logger.error(f"音频播放失败: {wav_path}")
        except Exception as e:
            logger.error(f"音频播放器初始化或播放失败: {e}")

