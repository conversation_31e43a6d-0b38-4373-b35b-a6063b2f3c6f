# 开发者文档

## 项目架构

### 核心模块

#### 1. 窗口管理 (`src/window/`)
- `firefly/` - 主窗口实现
  - `FireflyWindow.py` - 主窗口类，处理用户交互
  - `FireflyWindowConfig.py` - 配置管理（已优化）
  - `menu.py` - 右键菜单实现
- `management/` - 管理界面
- `message/` - 消息弹窗
- `web/` - Web 组件（Live2D 支持）

#### 2. 动作事件系统 (`src/ActionEvent/`)
- `Actions.py` - 动作事件核心逻辑（已优化性能）
- `RoleProperties.py` - 角色属性定义

#### 3. 语音包管理 (`src/FireflyVoicePack/`)
- `main.py` - 语音包加载和播放（已优化错误处理）

#### 4. 媒体播放器 (`src/MediaPlayer/`)
- `player.py` - 音频播放器（已优化资源管理）

#### 5. 配置管理 (`src/config/`)
- `manager.py` - 统一配置管理器（新增）
- 支持缓存、线程安全、默认值等特性

#### 6. 扩展系统 (`src/extends/`)
- `extend.py` - 扩展基础框架
- `BatteryVoice/` - 电池语音提醒扩展

### 设计模式

1. **单例模式** - 配置管理器
2. **观察者模式** - Qt 信号槽机制
3. **工厂模式** - 动作事件创建
4. **策略模式** - 不同时间段的语音选择

## 代码规范

### 命名规范
- 类名：PascalCase (`MainWindow`)
- 方法名：camelCase (`playNextImage`)
- 变量名：camelCase (`currentBgImage`)
- 常量名：UPPER_CASE (`CONFIG_FILE`)
- 文件名：snake_case (`action_pictures.json`)

### 注释规范
```python
def method_name(self, param: str) -> bool:
    """
    方法描述
    :param param: 参数描述
    :return: 返回值描述
    """
    pass
```

### 错误处理
- 使用具体的异常类型而不是通用的 `Exception`
- 记录详细的错误信息到日志
- 提供合理的默认值或降级处理

## 性能优化

### 已实现的优化

1. **动作系统优化**
   - 使用索引代替 `pop(0)` 和 `append` 操作
   - 实现懒加载，避免启动时加载所有动作数据
   - 添加缓存机制，减少文件系统访问

2. **配置管理优化**
   - 统一配置管理器，支持缓存
   - 线程安全的配置访问
   - 默认值合并机制

3. **资源管理优化**
   - 音频播放器资源自动清理
   - 图片加载错误处理
   - 内存泄漏预防

### 性能监控

使用 `loguru` 记录性能关键点：
```python
import time
from loguru import logger

start_time = time.time()
# 执行操作
logger.info(f"操作耗时: {time.time() - start_time:.3f}s")
```

## 扩展开发

### 创建新扩展

1. 在 `src/extends/` 下创建扩展目录
2. 实现扩展类，继承 `ExtendType`
3. 创建 `info.json` 配置文件
4. 在管理界面中注册扩展

### 扩展接口

```python
class MyExtension(ExtendType):
    def start(self) -> bool:
        """启动扩展"""
        pass
    
    def stop(self) -> bool:
        """停止扩展"""
        pass
    
    def settingWindow(self) -> None:
        """打开设置窗口"""
        pass
```

## 测试指南

### 单元测试

创建测试文件 `tests/test_module.py`：
```python
import unittest
from src.config import config_manager

class TestConfigManager(unittest.TestCase):
    def test_get_config(self):
        config = config_manager.get_config("main")
        self.assertIsInstance(config, dict)
    
    def test_set_value(self):
        result = config_manager.set_value("main", "test_key", "test_value")
        self.assertTrue(result)
```

### 集成测试

测试完整的用户交互流程：
1. 应用启动
2. 动作切换
3. 语音播放
4. 配置修改
5. 应用关闭

## 构建和部署

### 开发环境设置

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 运行应用
python MyFlowingFireflyWife.py
```

### 打包发布

```bash
# 使用 PyInstaller 打包
pyinstaller --onefile --add-data "src/*;." MyFlowingFireflyWife.py

# 或使用批处理脚本
build-pyinstaller.bat
```

### 发布检查清单

- [ ] 所有功能正常工作
- [ ] 无内存泄漏
- [ ] 错误处理完善
- [ ] 日志记录充分
- [ ] 配置文件完整
- [ ] 资源文件包含
- [ ] 文档更新

## 调试技巧

### 日志调试
```python
from loguru import logger

# 添加详细日志
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
```

### Qt 调试
```python
# 启用 Qt 调试输出
import os
os.environ['QT_LOGGING_RULES'] = '*=true'
```

### 性能分析
```python
import cProfile
import pstats

# 性能分析
profiler = cProfile.Profile()
profiler.enable()
# 执行代码
profiler.disable()

# 查看结果
stats = pstats.Stats(profiler)
stats.sort_stats('cumulative')
stats.print_stats(10)
```

## 常见问题

### Q: 如何添加新的动作？
A: 1. 在配置文件中添加动作定义 2. 在 Actions.py 中添加对应方法 3. 准备动作图片资源

### Q: 如何优化启动速度？
A: 1. 使用懒加载 2. 减少启动时的文件操作 3. 优化资源加载顺序

### Q: 如何处理内存泄漏？
A: 1. 及时释放 Qt 对象 2. 清理线程资源 3. 使用弱引用避免循环引用

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码审查要点

- 代码风格一致性
- 错误处理完善性
- 性能影响评估
- 测试覆盖率
- 文档更新
