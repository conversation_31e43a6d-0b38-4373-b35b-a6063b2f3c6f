# -*- coding: utf-8 -*-
"""
简化版萤火虫桌面宠物
"""
import sys
import os
from loguru import logger
from PySide6.QtWidgets import QApplication, QMainWindow, QLabel, QMenu
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QMouseEvent, QAction

DEF_IMG = os.path.join(os.getcwd(), "data", "assets", "images", "firefly", "default", "bg.png")

class SimpleFireflyWindow(QMainWindow):
    def __init__(self, app: QApplication) -> None:
        """简化版主窗口"""
        super().__init__()
        self.app = app
        
        # 基本状态
        self.drag_pos = None
        self.current_action = "Standby"
        
        # 设置窗口属性
        flags = Qt.WindowType.FramelessWindowHint | Qt.WindowType.Tool | Qt.WindowType.WindowStaysOnTopHint
        self.setWindowFlags(flags)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        self.setWindowTitle("MyFlowingFireflyWife")

        # 创建基本UI
        self.label = QLabel(self)
        self.load_image()
        
        # 设置鼠标跟踪
        self.setMouseTracking(True)
        
        # 延迟初始化右键菜单
        self.context_menu = None
        QTimer.singleShot(500, self.init_menu)
        
        logger.info("简化版主窗口初始化完成")

    def load_image(self):
        """加载图片"""
        try:
            if os.path.exists(DEF_IMG):
                pixmap = QPixmap(DEF_IMG)
                if not pixmap.isNull():
                    # 缩小图片
                    scaled_pixmap = pixmap.scaled(
                        pixmap.width() // 2,
                        pixmap.height() // 2,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.label.setPixmap(scaled_pixmap)
                    self.label.resize(scaled_pixmap.size())
                    self.resize(scaled_pixmap.size())
                    logger.info("图片加载成功")
                else:
                    logger.error("图片加载失败")
                    self.create_default_label()
            else:
                logger.warning(f"图片文件不存在: {DEF_IMG}")
                self.create_default_label()
        except Exception as e:
            logger.error(f"加载图片时出错: {e}")
            self.create_default_label()

    def create_default_label(self):
        """创建默认标签"""
        self.label.setText("萤火虫")
        self.label.setStyleSheet("background-color: rgba(255, 255, 255, 100); color: black; padding: 20px;")
        self.label.resize(100, 100)
        self.resize(100, 100)

    def init_menu(self):
        """初始化右键菜单"""
        try:
            self.context_menu = QMenu(self)
            
            # 添加基本菜单项
            action_sleep = QAction("睡觉", self)
            action_sleep.triggered.connect(self.perform_sleep)
            self.context_menu.addAction(action_sleep)
            
            action_dance = QAction("跳舞", self)
            action_dance.triggered.connect(self.perform_dance)
            self.context_menu.addAction(action_dance)
            
            self.context_menu.addSeparator()
            
            action_exit = QAction("退出", self)
            action_exit.triggered.connect(self.close)
            self.context_menu.addAction(action_exit)
            
            logger.info("右键菜单初始化成功")
            
        except Exception as e:
            logger.error(f"右键菜单初始化失败: {e}")

    def perform_sleep(self):
        """执行睡觉动作"""
        logger.info("执行睡觉动作")
        self.current_action = "sleep"

    def perform_dance(self):
        """执行跳舞动作"""
        logger.info("执行跳舞动作")
        self.current_action = "dance"

    def contextMenuEvent(self, event):
        """右键菜单事件"""
        try:
            if self.context_menu:
                self.context_menu.exec(event.globalPos())
            else:
                logger.warning("右键菜单未初始化")
        except Exception as e:
            logger.error(f"右键菜单事件处理失败: {e}")

    def mousePressEvent(self, event: QMouseEvent) -> None:
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_pos = event.globalPos()
            event.accept()
            logger.info("鼠标按下")
        return super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent) -> None:
        """鼠标移动事件"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.drag_pos is not None:
            self.move(self.pos() + event.globalPos() - self.drag_pos)
            self.drag_pos = event.globalPos()
            event.accept()
        return super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent) -> None:
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_pos = None
            event.accept()
            logger.info("鼠标释放")
        return super().mouseReleaseEvent(event)

def main():
    """主函数"""
    logger.info("=== 简化版萤火虫启动 ===")
    
    # 导入 Qt 相关模块
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import Qt
    
    # 设置 Qt 属性
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_ShareOpenGLContexts)
    
    # 创建 QApplication
    app = QApplication(sys.argv)
    logger.info("QApplication 创建成功")
    
    try:
        logger.info("创建简化版主窗口...")
        window = SimpleFireflyWindow(app)
        logger.info("显示窗口...")
        window.show()
        logger.info("启动应用...")
        return app.exec()
    except Exception as e:
        logger.error(f"错误: {e}")
        import traceback
        logger.error(f"详情: {traceback.format_exc()}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
