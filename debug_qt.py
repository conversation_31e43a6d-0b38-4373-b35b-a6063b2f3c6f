# -*- coding: utf-8 -*-
"""
Qt 导入问题诊断脚本
"""
import sys
import os
from loguru import logger

def test_step_by_step():
    """逐步测试导入"""
    try:
        logger.info("=== Qt 导入问题诊断 ===")
        
        logger.info("步骤1: 导入基础 Qt 模块")
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        logger.info("✓ 基础 Qt 模块导入成功")

        logger.info("步骤2: 设置 Qt 属性")
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_ShareOpenGLContexts)
        logger.info("✓ Qt 属性设置成功")

        logger.info("步骤3: 创建 QApplication")
        app = QApplication(sys.argv)
        logger.info("✓ QApplication 创建成功")

        logger.info("步骤4: 测试导入 qfluentwidgets")
        try:
            import qfluentwidgets
            logger.info("✓ qfluentwidgets 导入成功")
        except Exception as e:
            logger.error(f"✗ qfluentwidgets 导入失败: {e}")
            return False

        logger.info("步骤5: 测试导入 src.window.message.interface")
        try:
            from src.window.message.interface import PopupInterface, DEF_IMG
            logger.info("✓ message.interface 导入成功")
        except Exception as e:
            logger.error(f"✗ message.interface 导入失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

        logger.info("步骤6: 测试导入 src.ActionEvent")
        try:
            from src.ActionEvent import ActionEvent
            logger.info("✓ ActionEvent 导入成功")
        except Exception as e:
            logger.error(f"✗ ActionEvent 导入失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

        logger.info("步骤7: 测试导入 src.window.firefly.menu")
        try:
            from src.window.firefly.menu import Menu
            logger.info("✓ menu 导入成功")
        except Exception as e:
            logger.error(f"✗ menu 导入失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

        logger.info("步骤8: 测试导入主窗口模块")
        try:
            from src.window.firefly.FireflyWindow import MainWindow
            logger.info("✓ 主窗口模块导入成功")
        except Exception as e:
            logger.error(f"✗ 主窗口模块导入失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

        logger.info("步骤9: 创建主窗口")
        try:
            window = MainWindow(app)
            logger.info("✓ 主窗口创建成功")
        except Exception as e:
            logger.error(f"✗ 主窗口创建失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

        logger.info("步骤10: 显示主窗口")
        try:
            window.show()
            logger.info("✓ 主窗口显示成功")
        except Exception as e:
            logger.error(f"✗ 主窗口显示失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

        logger.info("=== 所有测试通过，启动应用 ===")
        return app.exec()

    except Exception as e:
        logger.error(f"测试过程中出现未知错误: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    sys.exit(test_step_by_step())
