{"Version": 3, "Meta": {"Duration": 201.967, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 31, "TotalSegmentCount": 727, "TotalPointCount": 2146, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 1, 26, 0, 52, 0, 78, 0, 1, 78.167, 0, 78.333, 30, 78.5, 30, 0, 201.967, 30]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.5, 0, 1, -30, 1.5, -30, 1, 2.333, -30, 3.167, 30, 4, 30, 1, 4.833, 30, 5.667, -30, 6.5, -30, 1, 7.333, -30, 8.167, 30, 9, 30, 1, 9.833, 30, 10.667, -30, 11.5, -30, 1, 12.333, -30, 13.167, 30, 14, 30, 1, 14.833, 30, 15.667, -30, 16.5, -30, 1, 17.333, -30, 18.167, 30, 19, 30, 1, 19.833, 30, 20.667, -30, 21.5, -30, 1, 22.333, -30, 23.167, 30, 24, 30, 1, 24.833, 30, 25.667, -30, 26.5, -30, 1, 27.333, -30, 28.167, 30, 29, 30, 1, 29.833, 30, 30.667, -30, 31.5, -30, 1, 32.333, -30, 33.167, 30, 34, 30, 1, 34.833, 30, 35.667, -30, 36.5, -30, 1, 37.333, -30, 38.167, 30, 39, 30, 1, 39.833, 30, 40.667, -30, 41.5, -30, 1, 42.333, -30, 43.167, 30, 44, 30, 1, 44.833, 30, 45.667, -30, 46.5, -30, 1, 47.333, -30, 48.167, 30, 49, 30, 1, 49.833, 30, 50.667, -30, 51.5, -30, 1, 52.333, -30, 53.167, 30, 54, 30, 1, 54.833, 30, 55.667, -30, 56.5, -30, 1, 57.333, -30, 58.167, 30, 59, 30, 1, 59.833, 30, 60.667, -30, 61.5, -30, 1, 62.333, -30, 63.167, 30, 64, 30, 1, 64.833, 30, 65.667, -30, 66.5, -30, 1, 67.333, -30, 68.167, 30, 69, 30, 1, 69.833, 30, 70.667, -30, 71.5, -30, 1, 72.333, -30, 73.167, 30, 74, 30, 1, 74.833, 30, 75.667, -30, 76.5, -30, 1, 77.333, -30, 78.167, 30, 79, 30, 1, 79.833, 30, 80.667, -30, 81.5, -30, 1, 82.333, -30, 83.167, 30, 84, 30, 1, 84.833, 30, 85.667, -30, 86.5, -30, 1, 87.333, -30, 88.167, 30, 89, 30, 1, 89.833, 30, 90.667, -30, 91.5, -30, 1, 92.333, -30, 93.167, 30, 94, 30, 1, 94.833, 30, 95.667, -30, 96.5, -30, 1, 97.333, -30, 98.167, 30, 99, 30, 1, 99.833, 30, 100.667, -30, 101.5, -30, 1, 102.333, -30, 103.167, 30, 104, 30, 1, 104.833, 30, 105.667, -30, 106.5, -30, 1, 107.333, -30, 108.167, 30, 109, 30, 1, 109.833, 30, 110.667, -30, 111.5, -30, 1, 112.333, -30, 113.167, 30, 114, 30, 1, 114.833, 30, 115.667, -30, 116.5, -30, 1, 117.333, -30, 118.167, 30, 119, 30, 1, 119.833, 30, 120.667, -30, 121.5, -30, 1, 122.333, -30, 123.167, 30, 124, 30, 1, 124.833, 30, 125.667, -30, 126.5, -30, 1, 127.333, -30, 128.167, 30, 129, 30, 1, 129.833, 30, 130.667, -30, 131.5, -30, 1, 132.333, -30, 133.167, 30, 134, 30, 1, 134.833, 30, 135.667, -30, 136.5, -30, 1, 137.333, -30, 138.167, 30, 139, 30, 1, 139.833, 30, 140.667, -30, 141.5, -30, 1, 142.333, -30, 143.167, 30, 144, 30, 1, 144.833, 30, 145.667, -30, 146.5, -30, 1, 147.333, -30, 148.167, 30, 149, 30, 1, 149.833, 30, 150.667, -30, 151.5, -30, 1, 152.333, -30, 153.167, 30, 154, 30, 1, 154.833, 30, 155.667, -30, 156.5, -30, 1, 157.333, -30, 158.167, 30, 159, 30, 1, 159.833, 30, 160.667, -30, 161.5, -30, 1, 162.333, -30, 163.167, 30, 164, 30, 1, 164.833, 30, 165.667, -30, 166.5, -30, 1, 167.333, -30, 168.167, 30, 169, 30, 1, 169.833, 30, 170.667, -30, 171.5, -30, 1, 172.333, -30, 173.167, 30, 174, 30, 1, 174.833, 30, 175.667, -30, 176.5, -30, 1, 177.333, -30, 178.167, 30, 179, 30, 1, 179.833, 30, 180.667, -30, 181.5, -30, 1, 182.333, -30, 183.167, 30, 184, 30, 1, 184.833, 30, 185.667, -30, 186.5, -30, 1, 187.333, -30, 188.167, 30, 189, 30, 1, 189.833, 30, 190.667, -30, 191.5, -30, 1, 192.333, -30, 193.167, 30, 194, 30, 1, 194.833, 30, 195.667, -30, 196.5, -30, 1, 197.333, -30, 198.167, 30, 199, 30, 1, 199.833, 30, 200.667, 0, 201.5, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.089, 1, 0.178, 0, 0.267, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.444, 0, 0.889, 0, 1.333, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.089, 1, 0.178, 0, 0.267, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.444, 0, 0.889, 0, 1.333, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 12, 0, 1, 12.056, 0, 12.111, 1, 12.167, 1, 1, 34.111, 1, 56.056, 1, 78, 1, 1, 78.167, 1, 78.333, 0, 78.5, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0, 1, 12.056, 0, 12.111, -1, 12.167, -1, 1, 34.111, -1, 56.056, -1, 78, -1, 1, 78.167, -1, 78.333, 0, 78.5, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 12, 0, 1, 12.056, 0, 12.111, 1, 12.167, 1, 1, 34.111, 1, 56.056, 1, 78, 1, 1, 78.167, 1, 78.333, 0, 78.5, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0, 1, 12.056, 0, 12.111, -1, 12.167, -1, 1, 34.111, -1, 56.056, -1, 78, -1, 1, 78.167, -1, 78.333, 0, 78.5, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 4.111, 0, 8.222, 0, 12.333, 0, 1, 14.333, 0, 16.333, 0, 18.333, 0, 1, 20.444, 0, 22.556, 0, 24.667, 0, 1, 46.667, 0, 68.667, 0, 90.667, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 4.111, 0, 8.222, 0, 12.333, 0, 1, 12.5, 0, 12.667, 0, 12.833, 0, 1, 12.889, 0, 12.944, 0.5, 13, 0.5, 1, 13.111, 0.5, 13.222, 0, 13.333, 0, 1, 13.389, 0, 13.444, 0, 13.5, 0, 1, 13.556, 0, 13.611, 0.8, 13.667, 0.8, 1, 13.778, 0.8, 13.889, 0, 14, 0, 1, 14.056, 0, 14.111, 0, 14.167, 0, 1, 14.222, 0, 14.278, 0.8, 14.333, 0.8, 1, 14.444, 0.8, 14.556, 0, 14.667, 0, 1, 14.722, 0, 14.778, 0, 14.833, 0, 1, 14.889, 0, 14.944, 0.7, 15, 0.7, 1, 15.111, 0.7, 15.222, 0, 15.333, 0, 1, 15.389, 0, 15.444, 0.9, 15.5, 0.9, 1, 15.611, 0.9, 15.722, 0, 15.833, 0, 1, 15.944, 0, 16.056, 0, 16.167, 0, 1, 16.222, 0, 16.278, 0.7, 16.333, 0.7, 1, 16.444, 0.7, 16.556, 0, 16.667, 0, 1, 16.722, 0, 16.778, 0.8, 16.833, 0.8, 1, 16.944, 0.8, 17.056, 0, 17.167, 0, 1, 17.222, 0, 17.278, 0.4, 17.333, 0.4, 1, 17.444, 0.4, 17.556, 0, 17.667, 0, 1, 17.889, 0, 18.111, 0, 18.333, 0, 1, 18.389, 0, 18.444, 0, 18.5, 0, 1, 18.556, 0, 18.611, 0.7, 18.667, 0.7, 1, 18.833, 0.7, 19, 0, 19.167, 0, 1, 19.222, 0, 19.278, 0.5, 19.333, 0.5, 1, 19.5, 0.5, 19.667, 0, 19.833, 0, 1, 19.889, 0, 19.944, 0.5, 20, 0.5, 1, 20.167, 0.5, 20.333, 0, 20.5, 0, 1, 20.556, 0, 20.611, 0, 20.667, 0, 1, 20.778, 0, 20.889, 0.8, 21, 0.8, 1, 21.056, 0.8, 21.111, 0, 21.167, 0, 1, 21.278, 0, 21.389, 0.682, 21.5, 0.7, 1, 22, 0.781, 22.5, 0.8, 23, 0.8, 1, 23.167, 0.8, 23.333, 0, 23.5, 0, 1, 23.889, 0, 24.278, 0, 24.667, 0, 1, 24.722, 0, 24.778, 0.7, 24.833, 0.7, 1, 24.889, 0.7, 24.944, 0, 25, 0, 1, 25.056, 0, 25.111, 0, 25.167, 0, 1, 25.278, 0, 25.389, 0.7, 25.5, 0.7, 1, 25.556, 0.7, 25.611, 0, 25.667, 0, 1, 25.722, 0, 25.778, 0, 25.833, 0, 1, 25.944, 0, 26.056, 0.7, 26.167, 0.7, 1, 26.222, 0.7, 26.278, 0, 26.333, 0, 1, 26.389, 0, 26.444, 0, 26.5, 0, 1, 26.611, 0, 26.722, 0, 26.833, 0, 1, 26.889, 0, 26.944, 0.6, 27, 0.6, 1, 27.056, 0.6, 27.111, 0, 27.167, 0, 1, 27.278, 0, 27.389, 0.8, 27.5, 0.8, 1, 27.667, 0.8, 27.833, 0.8, 28, 0.8, 1, 28.111, 0.8, 28.222, 0, 28.333, 0, 1, 28.411, 0, 28.489, 0, 28.567, 0, 1, 28.622, 0, 28.678, 0.6, 28.733, 0.6, 1, 28.789, 0.6, 28.844, 0, 28.9, 0, 1, 28.956, 0, 29.011, 0.8, 29.067, 0.8, 1, 29.122, 0.8, 29.178, 0, 29.233, 0, 1, 29.289, 0, 29.344, 0.4, 29.4, 0.4, 1, 29.456, 0.4, 29.511, 0, 29.567, 0, 1, 29.733, 0, 29.9, 0, 30.067, 0, 1, 30.122, 0, 30.178, 0.7, 30.233, 0.7, 1, 30.289, 0.7, 30.344, 0.714, 30.4, 0.6, 1, 30.456, 0.486, 30.511, 0, 30.567, 0, 1, 30.622, 0, 30.678, 0.6, 30.733, 0.6, 1, 31.044, 0.6, 31.356, 0.6, 31.667, 0.6, 1, 31.778, 0.6, 31.889, 0, 32, 0, 1, 32.111, 0, 32.222, 0, 32.333, 0, 1, 32.444, 0, 32.556, 0.6, 32.667, 0.6, 1, 32.722, 0.6, 32.778, 0, 32.833, 0, 1, 32.889, 0, 32.944, 0.5, 33, 0.5, 1, 33.056, 0.5, 33.111, 0, 33.167, 0, 1, 33.222, 0, 33.278, 0.6, 33.333, 0.6, 1, 33.389, 0.6, 33.444, 0, 33.5, 0, 1, 33.556, 0, 33.611, 0.7, 33.667, 0.7, 1, 33.944, 0.7, 34.222, 0.7, 34.5, 0.7, 1, 34.556, 0.7, 34.611, 0, 34.667, 0, 1, 35.222, 0, 35.778, 0, 36.333, 0, 1, 36.389, 0, 36.444, 0.6, 36.5, 0.6, 1, 36.556, 0.6, 36.611, 0, 36.667, 0, 1, 36.722, 0, 36.778, 0.7, 36.833, 0.7, 1, 36.889, 0.7, 36.944, 0, 37, 0, 1, 37.056, 0, 37.111, 0.4, 37.167, 0.4, 1, 37.222, 0.4, 37.278, 0, 37.333, 0, 1, 37.389, 0, 37.444, 0.5, 37.5, 0.5, 1, 37.556, 0.5, 37.611, 0, 37.667, 0, 1, 37.722, 0, 37.778, 0.4, 37.833, 0.4, 1, 37.889, 0.4, 37.944, 0, 38, 0, 1, 38.056, 0, 38.111, 0.7, 38.167, 0.7, 1, 38.222, 0.7, 38.278, 0, 38.333, 0, 1, 38.389, 0, 38.444, 0.6, 38.5, 0.6, 1, 38.667, 0.6, 38.833, 0, 39, 0, 1, 39.056, 0, 39.111, 0, 39.167, 0, 1, 39.278, 0, 39.389, 0.6, 39.5, 0.6, 1, 39.556, 0.6, 39.611, 0, 39.667, 0, 1, 39.722, 0, 39.778, 0.7, 39.833, 0.7, 1, 39.889, 0.7, 39.944, 0, 40, 0, 1, 40.056, 0, 40.111, 0.6, 40.167, 0.6, 1, 40.222, 0.6, 40.278, 0, 40.333, 0, 1, 40.389, 0, 40.444, 0.7, 40.5, 0.7, 1, 40.556, 0.7, 40.611, 0, 40.667, 0, 1, 40.722, 0, 40.778, 0.8, 40.833, 0.8, 1, 40.889, 0.8, 40.944, 0, 41, 0, 1, 41.056, 0, 41.111, 0.8, 41.167, 0.8, 1, 41.222, 0.8, 41.278, 0, 41.333, 0, 1, 41.389, 0, 41.444, 0.5, 41.5, 0.5, 1, 41.667, 0.5, 41.833, 0.5, 42, 0.5, 1, 42.056, 0.5, 42.111, 0, 42.167, 0, 1, 42.278, 0, 42.389, 0.4, 42.5, 0.4, 1, 42.556, 0.4, 42.611, 0, 42.667, 0, 1, 42.722, 0, 42.778, 0.8, 42.833, 0.8, 1, 42.944, 0.8, 43.056, 0.5, 43.167, 0.5, 1, 43.278, 0.5, 43.389, 0.7, 43.5, 0.7, 1, 43.556, 0.7, 43.611, 0, 43.667, 0, 1, 43.778, 0, 43.889, 0.8, 44, 0.8, 1, 44.056, 0.8, 44.111, 0, 44.167, 0, 1, 44.222, 0, 44.278, 0.6, 44.333, 0.6, 1, 44.389, 0.6, 44.444, 0, 44.5, 0, 1, 44.556, 0, 44.611, 0.5, 44.667, 0.5, 1, 44.722, 0.5, 44.778, 0, 44.833, 0, 1, 44.889, 0, 44.944, 0.7, 45, 0.7, 1, 45.078, 0.7, 45.156, 0, 45.233, 0, 1, 45.322, 0, 45.411, 0.8, 45.5, 0.8, 1, 45.556, 0.8, 45.611, 0, 45.667, 0, 1, 45.722, 0, 45.778, 0.7, 45.833, 0.7, 1, 46.333, 0.7, 46.833, 0.7, 47.333, 0.7, 1, 47.389, 0.7, 47.444, 0, 47.5, 0, 1, 47.944, 0, 48.389, 0, 48.833, 0, 1, 48.944, 0, 49.056, 0.5, 49.167, 0.5, 1, 49.222, 0.5, 49.278, 0, 49.333, 0, 1, 49.389, 0, 49.444, 0.8, 49.5, 0.8, 1, 49.556, 0.8, 49.611, 0, 49.667, 0, 1, 49.722, 0, 49.778, 0.7, 49.833, 0.7, 1, 49.889, 0.7, 49.944, 0, 50, 0, 1, 50.056, 0, 50.111, 0.5, 50.167, 0.5, 1, 50.222, 0.5, 50.278, 0, 50.333, 0, 1, 50.389, 0, 50.444, 0.7, 50.5, 0.7, 1, 50.556, 0.7, 50.611, 0, 50.667, 0, 1, 50.722, 0, 50.778, 0.5, 50.833, 0.5, 1, 50.889, 0.5, 50.944, 0, 51, 0, 1, 51.056, 0, 51.111, 0.4, 51.167, 0.4, 1, 51.222, 0.4, 51.278, 0, 51.333, 0, 1, 51.389, 0, 51.444, 0.6, 51.5, 0.6, 1, 52, 0.6, 52.5, 0.6, 53, 0.6, 1, 53.056, 0.6, 53.111, 0, 53.167, 0, 1, 53.222, 0, 53.278, 0.6, 53.333, 0.6, 1, 53.389, 0.6, 53.444, 0, 53.5, 0, 1, 53.556, 0, 53.611, 0.5, 53.667, 0.5, 1, 53.722, 0.5, 53.778, 0, 53.833, 0, 1, 53.889, 0, 53.944, 0.7, 54, 0.7, 1, 54.056, 0.7, 54.111, 0, 54.167, 0, 1, 54.222, 0, 54.278, 0.4, 54.333, 0.4, 1, 54.389, 0.4, 54.444, 0, 54.5, 0, 1, 54.556, 0, 54.611, 0.7, 54.667, 0.7, 1, 55.056, 0.7, 55.444, 0.676, 55.833, 0.6, 1, 55.889, 0.589, 55.944, 0, 56, 0, 1, 56.111, 0, 56.222, 0.6, 56.333, 0.6, 1, 56.389, 0.6, 56.444, 0, 56.5, 0, 1, 56.556, 0, 56.611, 0.5, 56.667, 0.5, 1, 56.722, 0.5, 56.778, 0, 56.833, 0, 1, 56.889, 0, 56.944, 0.7, 57, 0.7, 1, 57.056, 0.7, 57.111, 0, 57.167, 0, 1, 57.222, 0, 57.278, 0.8, 57.333, 0.8, 1, 57.389, 0.8, 57.444, 0, 57.5, 0, 1, 57.556, 0, 57.611, 0.6, 57.667, 0.6, 1, 58.333, 0.6, 59, 0.6, 59.667, 0.6, 1, 59.778, 0.6, 59.889, 0, 60, 0, 1, 60.333, 0, 60.667, 0, 61, 0, 1, 61.111, 0, 61.222, 0.6, 61.333, 0.6, 1, 61.389, 0.6, 61.444, 0, 61.5, 0, 1, 61.556, 0, 61.611, 0.7, 61.667, 0.7, 1, 61.722, 0.7, 61.778, 0, 61.833, 0, 1, 61.889, 0, 61.944, 0.8, 62, 0.8, 1, 62.056, 0.8, 62.111, 0, 62.167, 0, 1, 62.222, 0, 62.278, 0.5, 62.333, 0.5, 1, 62.389, 0.5, 62.444, 0, 62.5, 0, 1, 62.556, 0, 62.611, 0.8, 62.667, 0.8, 1, 62.722, 0.8, 62.778, 0, 62.833, 0, 1, 62.889, 0, 62.944, 0.5, 63, 0.5, 1, 63.056, 0.5, 63.111, 0, 63.167, 0, 1, 63.222, 0, 63.278, 0.4, 63.333, 0.4, 1, 63.389, 0.4, 63.444, 0, 63.5, 0, 1, 63.556, 0, 63.611, 0.6, 63.667, 0.6, 1, 64.056, 0.6, 64.444, 0.6, 64.833, 0.6, 1, 64.889, 0.6, 64.944, 0, 65, 0, 1, 65.111, 0, 65.222, 0, 65.333, 0, 1, 65.389, 0, 65.444, 0.7, 65.5, 0.7, 1, 65.556, 0.7, 65.611, 0, 65.667, 0, 1, 65.722, 0, 65.778, 0.5, 65.833, 0.5, 1, 65.889, 0.5, 65.944, 0, 66, 0, 1, 66.056, 0, 66.111, 0.8, 66.167, 0.8, 1, 66.222, 0.8, 66.278, 0, 66.333, 0, 1, 66.389, 0, 66.444, 0.5, 66.5, 0.5, 1, 66.556, 0.5, 66.611, 0, 66.667, 0, 1, 66.722, 0, 66.778, 0.7, 66.833, 0.7, 1, 67.111, 0.7, 67.389, 0.7, 67.667, 0.7, 1, 67.722, 0.7, 67.778, 0.6, 67.833, 0.6, 1, 67.889, 0.6, 67.944, 0.6, 68, 0.6, 1, 68.056, 0.6, 68.111, 0.8, 68.167, 0.8, 1, 68.278, 0.8, 68.389, 0, 68.5, 0, 1, 68.556, 0, 68.611, 0.8, 68.667, 0.8, 1, 68.722, 0.8, 68.778, 0, 68.833, 0, 1, 68.889, 0, 68.944, 0.8, 69, 0.8, 1, 69.111, 0.8, 69.222, 0.796, 69.333, 0.7, 1, 69.389, 0.652, 69.444, 0, 69.5, 0, 1, 69.556, 0, 69.611, 0.8, 69.667, 0.8, 1, 69.722, 0.8, 69.778, 0, 69.833, 0, 1, 69.889, 0, 69.944, 0.6, 70, 0.6, 1, 70.333, 0.6, 70.667, 0.6, 71, 0.6, 1, 71.056, 0.6, 71.111, 0, 71.167, 0, 1, 73.667, 0, 76.167, 0, 78.667, 0, 1, 78.722, 0, 78.778, 0.7, 78.833, 0.7, 1, 78.944, 0.7, 79.056, 0, 79.167, 0, 1, 79.222, 0, 79.278, 0.5, 79.333, 0.5, 1, 79.444, 0.5, 79.556, 0, 79.667, 0, 1, 79.722, 0, 79.778, 0, 79.833, 0, 1, 79.889, 0, 79.944, 0.5, 80, 0.5, 1, 80.111, 0.5, 80.222, 0, 80.333, 0, 1, 80.5, 0, 80.667, 0, 80.833, 0, 1, 80.889, 0, 80.944, 0.7, 81, 0.7, 1, 81.111, 0.7, 81.222, 0, 81.333, 0, 1, 81.389, 0, 81.444, 0.7, 81.5, 0.7, 1, 81.611, 0.7, 81.722, 0.7, 81.833, 0.7, 1, 81.889, 0.7, 81.944, 0, 82, 0, 1, 82.111, 0, 82.222, 0, 82.333, 0, 1, 82.389, 0, 82.444, 0.5, 82.5, 0.5, 1, 82.556, 0.5, 82.611, 0, 82.667, 0, 1, 82.722, 0, 82.778, 0.7, 82.833, 0.7, 1, 82.944, 0.7, 83.056, 0, 83.167, 0, 1, 83.667, 0, 84.167, 0, 84.667, 0, 1, 84.722, 0, 84.778, 0.7, 84.833, 0.7, 1, 84.889, 0.7, 84.944, 0, 85, 0, 1, 85.111, 0, 85.222, 0, 85.333, 0, 1, 85.389, 0, 85.444, 0.7, 85.5, 0.7, 1, 85.556, 0.7, 85.611, 0, 85.667, 0, 1, 85.778, 0, 85.889, 0, 86, 0, 1, 86.056, 0, 86.111, 0.7, 86.167, 0.7, 1, 86.222, 0.7, 86.278, 0, 86.333, 0, 1, 86.5, 0, 86.667, 0, 86.833, 0, 1, 86.889, 0, 86.944, 0.7, 87, 0.7, 1, 87.056, 0.7, 87.111, 0, 87.167, 0, 1, 87.278, 0, 87.389, 0.7, 87.5, 0.7, 1, 88, 0.7, 88.5, 0.7, 89, 0.7, 1, 89.111, 0.7, 89.222, 0, 89.333, 0, 1, 89.778, 0, 90.222, 0, 90.667, 0, 1, 90.722, 0, 90.778, 0.8, 90.833, 0.8, 1, 90.867, 0.8, 90.9, 0.8, 90.933, 0.8, 1, 90.956, 0.8, 90.978, 0.4, 91, 0.4, 1, 91.033, 0.4, 91.067, 0.4, 91.1, 0.4, 1, 91.122, 0.4, 91.144, 0, 91.167, 0, 1, 91.278, 0, 91.389, 0, 91.5, 0, 1, 91.556, 0, 91.611, 0.7, 91.667, 0.7, 1, 91.778, 0.7, 91.889, 0, 92, 0, 1, 92.056, 0, 92.111, 0.7, 92.167, 0.7, 1, 92.222, 0.7, 92.278, 0.7, 92.333, 0.7, 1, 92.444, 0.7, 92.556, 0, 92.667, 0, 1, 92.778, 0, 92.889, 0, 93, 0, 1, 93.056, 0, 93.111, 0.7, 93.167, 0.7, 1, 93.222, 0.7, 93.278, 0, 93.333, 0, 1, 93.389, 0, 93.444, 0.6, 93.5, 0.6, 1, 93.611, 0.6, 93.722, 0, 93.833, 0, 1, 94.056, 0, 94.278, 0, 94.5, 0, 1, 94.556, 0, 94.611, 0.8, 94.667, 0.8, 1, 94.722, 0.8, 94.778, 0, 94.833, 0, 1, 94.889, 0, 94.944, 0.8, 95, 0.8, 1, 95.056, 0.8, 95.111, 0, 95.167, 0, 1, 95.389, 0, 95.611, 0, 95.833, 0, 1, 95.889, 0, 95.944, 0.7, 96, 0.7, 1, 96.056, 0.7, 96.111, 0.714, 96.167, 0.6, 1, 96.222, 0.486, 96.278, 0, 96.333, 0, 1, 96.389, 0, 96.444, 0.6, 96.5, 0.6, 1, 96.778, 0.6, 97.056, 0.6, 97.333, 0.6, 1, 97.389, 0.6, 97.444, 0, 97.5, 0, 1, 97.833, 0, 98.167, 0, 98.5, 0, 1, 98.556, 0, 98.611, 0.7, 98.667, 0.7, 1, 98.722, 0.7, 98.778, 0, 98.833, 0, 1, 98.889, 0, 98.944, 0.6, 99, 0.6, 1, 99.056, 0.6, 99.111, 0, 99.167, 0, 1, 99.222, 0, 99.278, 0.6, 99.333, 0.6, 1, 99.389, 0.6, 99.444, 0, 99.5, 0, 1, 99.556, 0, 99.611, 0.7, 99.667, 0.7, 1, 99.722, 0.7, 99.778, 0.7, 99.833, 0.7, 1, 99.889, 0.7, 99.944, 0, 100, 0, 1, 100.778, 0, 101.556, 0, 102.333, 0, 1, 102.389, 0, 102.444, 0.7, 102.5, 0.7, 1, 102.556, 0.7, 102.611, 0, 102.667, 0, 1, 102.722, 0, 102.778, 0.5, 102.833, 0.5, 1, 102.889, 0.5, 102.944, 0, 103, 0, 1, 103.056, 0, 103.111, 0.6, 103.167, 0.6, 1, 103.222, 0.6, 103.278, 0, 103.333, 0, 1, 103.389, 0, 103.444, 0.7, 103.5, 0.7, 1, 103.556, 0.7, 103.611, 0.1, 103.667, 0.1, 1, 103.722, 0.1, 103.778, 0.4, 103.833, 0.4, 1, 103.889, 0.4, 103.944, 0, 104, 0, 1, 104.056, 0, 104.111, 0.6, 104.167, 0.6, 1, 104.222, 0.6, 104.278, 0.1, 104.333, 0.1, 1, 104.389, 0.1, 104.444, 0.7, 104.5, 0.7, 1, 104.611, 0.7, 104.722, 0, 104.833, 0, 1, 105.056, 0, 105.278, 0, 105.5, 0, 1, 105.556, 0, 105.611, 0.7, 105.667, 0.7, 1, 105.722, 0.7, 105.778, 0, 105.833, 0, 1, 105.889, 0, 105.944, 0.7, 106, 0.7, 1, 106.056, 0.7, 106.111, 0, 106.167, 0, 1, 106.222, 0, 106.278, 0.5, 106.333, 0.5, 1, 106.389, 0.5, 106.444, 0.1, 106.5, 0.1, 1, 106.556, 0.1, 106.611, 0.7, 106.667, 0.7, 1, 106.722, 0.7, 106.778, 0, 106.833, 0, 1, 106.889, 0, 106.944, 0.5, 107, 0.5, 1, 107.056, 0.5, 107.111, 0, 107.167, 0, 1, 107.222, 0, 107.278, 0.7, 107.333, 0.7, 1, 107.389, 0.7, 107.444, 0, 107.5, 0, 1, 107.556, 0, 107.611, 0.4, 107.667, 0.4, 1, 107.722, 0.4, 107.778, 0, 107.833, 0, 1, 107.944, 0, 108.056, 0, 108.167, 0, 1, 108.222, 0, 108.278, 0.5, 108.333, 0.5, 1, 108.389, 0.5, 108.444, 0, 108.5, 0, 1, 108.556, 0, 108.611, 0.417, 108.667, 0.5, 1, 108.833, 0.75, 109, 0.8, 109.167, 0.8, 1, 109.333, 0.8, 109.5, 0.787, 109.667, 0.7, 1, 109.722, 0.671, 109.778, 0, 109.833, 0, 1, 109.889, 0, 109.944, 0.7, 110, 0.7, 1, 110.056, 0.7, 110.111, 0, 110.167, 0, 1, 110.222, 0, 110.278, 0.6, 110.333, 0.6, 1, 110.389, 0.6, 110.444, 0, 110.5, 0, 1, 110.556, 0, 110.611, 0.5, 110.667, 0.5, 1, 110.722, 0.5, 110.778, 0, 110.833, 0, 1, 110.889, 0, 110.944, 0.5, 111, 0.5, 1, 111.056, 0.5, 111.111, 0, 111.167, 0, 1, 111.222, 0, 111.278, 0.8, 111.333, 0.8, 1, 111.389, 0.8, 111.444, 0, 111.5, 0, 1, 111.556, 0, 111.611, 0.7, 111.667, 0.7, 1, 113.056, 0.7, 114.444, 0.7, 115.833, 0.7, 1, 115.889, 0.7, 115.944, 0, 116, 0, 1, 117.611, 0, 119.222, 0, 120.833, 0, 1, 120.944, 0, 121.056, 0.5, 121.167, 0.5, 1, 121.222, 0.5, 121.278, 0, 121.333, 0, 1, 121.389, 0, 121.444, 0.8, 121.5, 0.8, 1, 121.556, 0.8, 121.611, 0, 121.667, 0, 1, 121.722, 0, 121.778, 0.7, 121.833, 0.7, 1, 121.889, 0.7, 121.944, 0, 122, 0, 1, 122.056, 0, 122.111, 0.5, 122.167, 0.5, 1, 122.222, 0.5, 122.278, 0, 122.333, 0, 1, 122.389, 0, 122.444, 0.7, 122.5, 0.7, 1, 122.556, 0.7, 122.611, 0, 122.667, 0, 1, 122.722, 0, 122.778, 0.5, 122.833, 0.5, 1, 122.889, 0.5, 122.944, 0, 123, 0, 1, 123.056, 0, 123.111, 0.4, 123.167, 0.4, 1, 123.222, 0.4, 123.278, 0, 123.333, 0, 1, 123.389, 0, 123.444, 0.8, 123.5, 0.8, 1, 123.556, 0.8, 123.611, 0, 123.667, 0, 1, 123.722, 0, 123.778, 0.6, 123.833, 0.6, 1, 124.222, 0.6, 124.611, 0.6, 125, 0.6, 1, 125.056, 0.6, 125.111, 0, 125.167, 0, 1, 125.222, 0, 125.278, 0.6, 125.333, 0.6, 1, 125.389, 0.6, 125.444, 0, 125.5, 0, 1, 125.556, 0, 125.611, 0.5, 125.667, 0.5, 1, 125.722, 0.5, 125.778, 0, 125.833, 0, 1, 125.889, 0, 125.944, 0.7, 126, 0.7, 1, 126.056, 0.7, 126.111, 0, 126.167, 0, 1, 126.222, 0, 126.278, 0.4, 126.333, 0.4, 1, 126.389, 0.4, 126.444, 0, 126.5, 0, 1, 126.556, 0, 126.611, 0.7, 126.667, 0.7, 1, 127.056, 0.7, 127.444, 0.676, 127.833, 0.6, 1, 127.889, 0.589, 127.944, 0, 128, 0, 1, 128.111, 0, 128.222, 0.6, 128.333, 0.6, 1, 128.389, 0.6, 128.444, 0, 128.5, 0, 1, 128.556, 0, 128.611, 0.5, 128.667, 0.5, 1, 128.722, 0.5, 128.778, 0, 128.833, 0, 1, 128.889, 0, 128.944, 0.7, 129, 0.7, 1, 129.056, 0.7, 129.111, 0, 129.167, 0, 1, 129.222, 0, 129.278, 0.8, 129.333, 0.8, 1, 129.389, 0.8, 129.444, 0, 129.5, 0, 1, 129.556, 0, 129.611, 0.8, 129.667, 0.8, 1, 130.556, 0.8, 131.444, 0.8, 132.333, 0.8, 1, 132.444, 0.8, 132.556, 0, 132.667, 0, 1, 132.722, 0, 132.778, 0, 132.833, 0, 1, 132.889, 0, 132.944, 0, 133, 0, 1, 133.056, 0, 133.111, 0.5, 133.167, 0.5, 1, 133.222, 0.5, 133.278, 0, 133.333, 0, 1, 133.389, 0, 133.444, 0.8, 133.5, 0.8, 1, 133.556, 0.8, 133.611, 0, 133.667, 0, 1, 133.722, 0, 133.778, 0.7, 133.833, 0.7, 1, 133.889, 0.7, 133.944, 0, 134, 0, 1, 134.056, 0, 134.111, 0.5, 134.167, 0.5, 1, 134.222, 0.5, 134.278, 0, 134.333, 0, 1, 134.389, 0, 134.444, 0.7, 134.5, 0.7, 1, 134.556, 0.7, 134.611, 0, 134.667, 0, 1, 134.722, 0, 134.778, 0.5, 134.833, 0.5, 1, 134.889, 0.5, 134.944, 0, 135, 0, 1, 135.056, 0, 135.111, 0.4, 135.167, 0.4, 1, 135.222, 0.4, 135.278, 0, 135.333, 0, 1, 135.389, 0, 135.444, 0.6, 135.5, 0.6, 1, 136.056, 0.6, 136.611, 0.6, 137.167, 0.6, 1, 137.222, 0.6, 137.278, 0, 137.333, 0, 1, 137.389, 0, 137.444, 0.6, 137.5, 0.6, 1, 137.556, 0.6, 137.611, 0, 137.667, 0, 1, 137.722, 0, 137.778, 0.5, 137.833, 0.5, 1, 137.889, 0.5, 137.944, 0, 138, 0, 1, 138.056, 0, 138.111, 0.7, 138.167, 0.7, 1, 138.222, 0.7, 138.278, 0, 138.333, 0, 1, 138.389, 0, 138.444, 0.4, 138.5, 0.4, 1, 138.556, 0.4, 138.611, 0, 138.667, 0, 1, 138.722, 0, 138.778, 0.7, 138.833, 0.7, 1, 139.333, 0.7, 139.833, 0.674, 140.333, 0.6, 1, 140.389, 0.592, 140.444, 0, 140.5, 0, 1, 140.556, 0, 140.611, 0, 140.667, 0, 1, 140.722, 0, 140.778, 0.5, 140.833, 0.5, 1, 140.889, 0.5, 140.944, 0, 141, 0, 1, 141.056, 0, 141.111, 0.7, 141.167, 0.7, 1, 141.222, 0.7, 141.278, 0, 141.333, 0, 1, 141.389, 0, 141.444, 0, 141.5, 0, 1, 141.556, 0, 141.611, 0.8, 141.667, 0.8, 1, 141.722, 0.8, 141.778, 0, 141.833, 0, 1, 141.889, 0, 141.944, 0.6, 142, 0.6, 1, 142.611, 0.6, 143.222, 0.6, 143.833, 0.6, 1, 143.944, 0.6, 144.056, 0, 144.167, 0, 1, 148.5, 0, 152.833, 0, 157.167, 0, 1, 157.278, 0, 157.389, 0.5, 157.5, 0.5, 1, 157.556, 0.5, 157.611, 0, 157.667, 0, 1, 157.722, 0, 157.778, 0.8, 157.833, 0.8, 1, 157.889, 0.8, 157.944, 0, 158, 0, 1, 158.056, 0, 158.111, 0.7, 158.167, 0.7, 1, 158.222, 0.7, 158.278, 0, 158.333, 0, 1, 158.389, 0, 158.444, 0.5, 158.5, 0.5, 1, 158.556, 0.5, 158.611, 0, 158.667, 0, 1, 158.722, 0, 158.778, 0.7, 158.833, 0.7, 1, 158.889, 0.7, 158.944, 0, 159, 0, 1, 159.056, 0, 159.111, 0.5, 159.167, 0.5, 1, 159.222, 0.5, 159.278, 0, 159.333, 0, 1, 159.389, 0, 159.444, 0.4, 159.5, 0.4, 1, 159.556, 0.4, 159.611, 0, 159.667, 0, 1, 159.722, 0, 159.778, 0.6, 159.833, 0.6, 1, 160.278, 0.6, 160.722, 0.6, 161.167, 0.6, 1, 161.222, 0.6, 161.278, 0, 161.333, 0, 1, 161.389, 0, 161.444, 0.6, 161.5, 0.6, 1, 161.556, 0.6, 161.611, 0, 161.667, 0, 1, 161.722, 0, 161.778, 0.5, 161.833, 0.5, 1, 161.889, 0.5, 161.944, 0, 162, 0, 1, 162.056, 0, 162.111, 0.7, 162.167, 0.7, 1, 162.222, 0.7, 162.278, 0, 162.333, 0, 1, 162.389, 0, 162.444, 0.4, 162.5, 0.4, 1, 162.556, 0.4, 162.611, 0, 162.667, 0, 1, 162.722, 0, 162.778, 0.7, 162.833, 0.7, 1, 163.167, 0.7, 163.5, 0.677, 163.833, 0.6, 1, 163.889, 0.587, 163.944, 0, 164, 0, 1, 164.111, 0, 164.222, 0.6, 164.333, 0.6, 1, 164.389, 0.6, 164.444, 0, 164.5, 0, 1, 164.556, 0, 164.611, 0.5, 164.667, 0.5, 1, 164.722, 0.5, 164.778, 0, 164.833, 0, 1, 164.889, 0, 164.944, 0.7, 165, 0.7, 1, 165.056, 0.7, 165.111, 0, 165.167, 0, 1, 165.222, 0, 165.278, 0.8, 165.333, 0.8, 1, 165.389, 0.8, 165.444, 0, 165.5, 0, 1, 165.556, 0, 165.611, 0.6, 165.667, 0.6, 1, 166.278, 0.6, 166.889, 0.6, 167.5, 0.6, 1, 167.556, 0.6, 167.611, 0.8, 167.667, 0.8, 1, 167.833, 0.8, 168, 0.8, 168.167, 0.8, 1, 168.222, 0.8, 168.278, 0.771, 168.333, 0.6, 1, 168.444, 0.257, 168.556, 0, 168.667, 0, 1, 168.722, 0, 168.778, 0, 168.833, 0, 1, 168.889, 0, 168.944, 0.6, 169, 0.6, 1, 169.056, 0.6, 169.111, 0, 169.167, 0, 1, 169.222, 0, 169.278, 0.6, 169.333, 0.6, 1, 169.389, 0.6, 169.444, 0, 169.5, 0, 1, 169.556, 0, 169.611, 0.6, 169.667, 0.6, 1, 169.722, 0.6, 169.778, 0, 169.833, 0, 1, 169.889, 0, 169.944, 0.7, 170, 0.7, 1, 170.056, 0.7, 170.111, 0, 170.167, 0, 1, 170.222, 0, 170.278, 0.8, 170.333, 0.8, 1, 170.389, 0.8, 170.444, 0, 170.5, 0, 1, 170.556, 0, 170.611, 0.5, 170.667, 0.5, 1, 170.722, 0.5, 170.778, 0, 170.833, 0, 1, 170.889, 0, 170.944, 0.8, 171, 0.8, 1, 171.056, 0.8, 171.111, 0, 171.167, 0, 1, 171.222, 0, 171.278, 0.5, 171.333, 0.5, 1, 171.389, 0.5, 171.444, 0, 171.5, 0, 1, 171.556, 0, 171.611, 0.4, 171.667, 0.4, 1, 171.722, 0.4, 171.778, 0, 171.833, 0, 1, 171.889, 0, 171.944, 0.6, 172, 0.6, 1, 172.222, 0.6, 172.444, 0.6, 172.667, 0.6, 1, 172.722, 0.6, 172.778, 0, 172.833, 0, 1, 172.944, 0, 173.056, 0, 173.167, 0, 1, 173.222, 0, 173.278, 0.7, 173.333, 0.7, 1, 173.389, 0.7, 173.444, 0, 173.5, 0, 1, 173.556, 0, 173.611, 0.5, 173.667, 0.5, 1, 173.722, 0.5, 173.778, 0, 173.833, 0, 1, 173.889, 0, 173.944, 0.8, 174, 0.8, 1, 174.056, 0.8, 174.111, 0, 174.167, 0, 1, 174.222, 0, 174.278, 0.5, 174.333, 0.5, 1, 174.389, 0.5, 174.444, 0, 174.5, 0, 1, 174.556, 0, 174.611, 0.7, 174.667, 0.7, 1, 175, 0.7, 175.333, 0.7, 175.667, 0.7, 1, 175.722, 0.7, 175.778, 0.6, 175.833, 0.6, 1, 175.889, 0.6, 175.944, 0.7, 176, 0.7, 1, 177.222, 0.7, 178.444, 0.7, 179.667, 0.7, 1, 179.889, 0.7, 180.111, 0, 180.333, 0, 1, 180.389, 0, 180.444, 0.8, 180.5, 0.8, 1, 180.556, 0.8, 180.611, 0.8, 180.667, 0.8, 1, 180.722, 0.8, 180.778, 0, 180.833, 0, 1, 180.889, 0, 180.944, 0.6, 181, 0.6, 1, 181.389, 0.6, 181.778, 0.6, 182.167, 0.6, 1, 182.222, 0.6, 182.278, 0, 182.333, 0, 1, 183.556, 0, 184.778, 0, 186, 0, 1, 186.056, 0, 186.111, 0.8, 186.167, 0.8, 1, 186.222, 0.8, 186.278, 0.8, 186.333, 0.8, 1, 186.389, 0.8, 186.444, 0, 186.5, 0, 1, 186.556, 0, 186.611, 0.6, 186.667, 0.6, 1, 187.056, 0.6, 187.444, 0.6, 187.833, 0.6, 1, 187.889, 0.6, 187.944, 0, 188, 0, 0, 201.967, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.5, 0, 1, -4, 1.5, -4, 1, 2.333, -4, 3.167, 4, 4, 4, 1, 4.833, 4, 5.667, -4, 6.5, -4, 1, 7.333, -4, 8.167, 4, 9, 4, 1, 9.833, 4, 10.667, -4, 11.5, -4, 1, 12.333, -4, 13.167, 4, 14, 4, 1, 14.833, 4, 15.667, -4, 16.5, -4, 1, 17.333, -4, 18.167, 4, 19, 4, 1, 19.833, 4, 20.667, -4, 21.5, -4, 1, 22.333, -4, 23.167, 4, 24, 4, 1, 24.833, 4, 25.667, -4, 26.5, -4, 1, 27.333, -4, 28.167, 4, 29, 4, 1, 29.833, 4, 30.667, -4, 31.5, -4, 1, 32.333, -4, 33.167, 4, 34, 4, 1, 34.833, 4, 35.667, -4, 36.5, -4, 1, 37.333, -4, 38.167, 4, 39, 4, 1, 39.833, 4, 40.667, -4, 41.5, -4, 1, 42.333, -4, 43.167, 4, 44, 4, 1, 44.833, 4, 45.667, -4, 46.5, -4, 1, 47.333, -4, 48.167, 4, 49, 4, 1, 49.833, 4, 50.667, -4, 51.5, -4, 1, 52.333, -4, 53.167, 4, 54, 4, 1, 54.833, 4, 55.667, -4, 56.5, -4, 1, 57.333, -4, 58.167, 4, 59, 4, 1, 59.833, 4, 60.667, -4, 61.5, -4, 1, 62.333, -4, 63.167, 4, 64, 4, 1, 64.833, 4, 65.667, -4, 66.5, -4, 1, 67.333, -4, 68.167, 4, 69, 4, 1, 69.833, 4, 70.667, -4, 71.5, -4, 1, 72.333, -4, 73.167, 4, 74, 4, 1, 74.833, 4, 75.667, -4, 76.5, -4, 1, 77.333, -4, 78.167, 4, 79, 4, 1, 79.833, 4, 80.667, -4, 81.5, -4, 1, 82.333, -4, 83.167, 4, 84, 4, 1, 84.833, 4, 85.667, -4, 86.5, -4, 1, 87.333, -4, 88.167, 4, 89, 4, 1, 89.833, 4, 90.667, -4, 91.5, -4, 1, 92.333, -4, 93.167, 4, 94, 4, 1, 94.833, 4, 95.667, -4, 96.5, -4, 1, 97.333, -4, 98.167, 4, 99, 4, 1, 99.833, 4, 100.667, -4, 101.5, -4, 1, 102.333, -4, 103.167, 4, 104, 4, 1, 104.833, 4, 105.667, -4, 106.5, -4, 1, 107.333, -4, 108.167, 4, 109, 4, 1, 109.833, 4, 110.667, -4, 111.5, -4, 1, 112.333, -4, 113.167, 4, 114, 4, 1, 114.833, 4, 115.667, -4, 116.5, -4, 1, 117.333, -4, 118.167, 4, 119, 4, 1, 119.833, 4, 120.667, -4, 121.5, -4, 1, 122.333, -4, 123.167, 4, 124, 4, 1, 124.833, 4, 125.667, -4, 126.5, -4, 1, 127.333, -4, 128.167, 4, 129, 4, 1, 129.833, 4, 130.667, -4, 131.5, -4, 1, 132.333, -4, 133.167, 4, 134, 4, 1, 134.833, 4, 135.667, -4, 136.5, -4, 1, 137.333, -4, 138.167, 4, 139, 4, 1, 139.833, 4, 140.667, -4, 141.5, -4, 1, 142.333, -4, 143.167, 4, 144, 4, 1, 144.833, 4, 145.667, -4, 146.5, -4, 1, 147.333, -4, 148.167, 4, 149, 4, 1, 149.833, 4, 150.667, -4, 151.5, -4, 1, 152.333, -4, 153.167, 4, 154, 4, 1, 154.833, 4, 155.667, -4, 156.5, -4, 1, 157.333, -4, 158.167, 4, 159, 4, 1, 159.833, 4, 160.667, -4, 161.5, -4, 1, 162.333, -4, 163.167, 4, 164, 4, 1, 164.833, 4, 165.667, -4, 166.5, -4, 1, 167.333, -4, 168.167, 4, 169, 4, 1, 169.833, 4, 170.667, -4, 171.5, -4, 1, 172.333, -4, 173.167, 4, 174, 4, 1, 174.833, 4, 175.667, -4, 176.5, -4, 1, 177.333, -4, 178.167, 4, 179, 4, 1, 179.833, 4, 180.667, -4, 181.5, -4, 1, 182.333, -4, 183.167, 4, 184, 4, 1, 184.833, 4, 185.667, -4, 186.5, -4, 1, 187.333, -4, 188.167, 4, 189, 4, 1, 189.833, 4, 190.667, -4, 191.5, -4, 1, 192.333, -4, 193.167, 4, 194, 4, 1, 194.833, 4, 195.667, -4, 196.5, -4, 1, 197.333, -4, 198.167, 4, 199, 4, 1, 199.833, 4, 200.667, 0, 201.5, 0, 0, 201.967, 0]}]}