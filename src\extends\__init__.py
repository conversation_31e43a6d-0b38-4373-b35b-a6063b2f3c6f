from typing import Dict
from .extend import ExtendType

# 延迟导入，避免在模块加载时创建 Qt 对象
_allExtend: Dict[str, ExtendType] = {}
_initialized = False


def initExtend(extend: ExtendType) -> ExtendType:
    """初始化扩展"""
    extend = extend()
    if extend.InfoJson.isStatic is True:
        extend.start()
    return extend


def get_all_extends() -> Dict[str, ExtendType]:
    """获取所有扩展（延迟初始化）"""
    global _allExtend, _initialized

    if not _initialized:
        # 延迟导入，确保在 QApplication 创建后才导入
        from .BatteryVoice.main import batteryVoice

        _allExtend['BatteryVoice'] = initExtend(batteryVoice)
        _initialized = True

    return _allExtend


# 为了向后兼容，提供属性访问
class ExtendManager:
    @property
    def allExtend(self) -> Dict[str, ExtendType]:
        return get_all_extends()


# 创建管理器实例
extend_manager = ExtendManager()

# 向后兼容的接口
def get_extend(name: str) -> ExtendType:
    """获取指定扩展"""
    return get_all_extends().get(name)