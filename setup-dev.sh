#!/bin/bash

echo "========================================"
echo "MyFlowingFireflyWife 开发环境设置"
echo "========================================"

# 检查 Python 环境
echo "检查 Python 环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到 Python3，请确保 Python3 已安装"
    exit 1
fi

python3 --version

# 创建虚拟环境
echo "创建虚拟环境..."
if [ -d "venv" ]; then
    echo "虚拟环境已存在，跳过创建"
else
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "创建虚拟环境失败！"
        exit 1
    fi
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 升级 pip
echo "升级 pip..."
python -m pip install --upgrade pip

# 安装依赖
echo "安装依赖..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "安装依赖失败！"
    exit 1
fi

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p data/config
mkdir -p data/assets
mkdir -p data/audio
mkdir -p log

echo "========================================"
echo "开发环境设置完成！"
echo ""
echo "使用方法:"
echo "1. 激活虚拟环境: source venv/bin/activate"
echo "2. 运行应用: python MyFlowingFireflyWife.py"
echo "3. 退出虚拟环境: deactivate"
echo "========================================"
