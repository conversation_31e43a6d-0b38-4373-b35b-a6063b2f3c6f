# -*- coding: utf-8 -*-
"""
最小化测试版本
"""
import sys
import os
from loguru import logger

def main():
    """主函数"""
    logger.info("=== 最小化测试开始 ===")
    
    # 导入 Qt 相关模块
    from PySide6.QtWidgets import QApplication, QMainWindow, QLabel
    from PySide6.QtCore import Qt
    
    # 设置 Qt 属性
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_ShareOpenGLContexts)
    
    # 创建 QApplication
    app = QApplication(sys.argv)
    logger.info("QApplication 创建成功")
    
    # 创建最简单的主窗口
    class MinimalWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("Minimal Test")
            self.resize(300, 200)
            
            # 只添加一个标签
            self.label = QLabel("测试窗口", self)
            self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setCentralWidget(self.label)
    
    try:
        logger.info("创建最小窗口...")
        window = MinimalWindow()
        logger.info("显示窗口...")
        window.show()
        logger.info("启动应用...")
        return app.exec()
    except Exception as e:
        logger.error(f"错误: {e}")
        import traceback
        logger.error(f"详情: {traceback.format_exc()}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
