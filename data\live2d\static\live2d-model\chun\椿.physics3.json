{"Version": 3, "Meta": {"PhysicsSettingCount": 33, "TotalInputCount": 69, "TotalOutputCount": 74, "VertexCount": 120, "Fps": 60, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "左眼物理"}, {"Id": "PhysicsSetting2", "Name": "右眼物理"}, {"Id": "PhysicsSetting3", "Name": "左眼玉物理"}, {"Id": "PhysicsSetting4", "Name": "右眼玉物理"}, {"Id": "PhysicsSetting5", "Name": "眉毛眼Z控制"}, {"Id": "PhysicsSetting6", "Name": "嘴部眼型控制"}, {"Id": "PhysicsSetting7", "Name": "眉毛眼部控制"}, {"Id": "PhysicsSetting8", "Name": "眼球缓动X"}, {"Id": "PhysicsSetting9", "Name": "眼球缓动Y"}, {"Id": "PhysicsSetting10", "Name": "左耳物理1"}, {"Id": "PhysicsSetting11", "Name": "右耳物理1"}, {"Id": "PhysicsSetting12", "Name": "左耳物理2"}, {"Id": "PhysicsSetting13", "Name": "右耳物理2"}, {"Id": "PhysicsSetting14", "Name": "歪嘴"}, {"Id": "PhysicsSetting15", "Name": "身体X"}, {"Id": "PhysicsSetting16", "Name": "身体Y"}, {"Id": "PhysicsSetting17", "Name": "身体Y2"}, {"Id": "PhysicsSetting18", "Name": "身体Z"}, {"Id": "PhysicsSetting19", "Name": "左手"}, {"Id": "PhysicsSetting20", "Name": "右手"}, {"Id": "PhysicsSetting21", "Name": "胸部X"}, {"Id": "PhysicsSetting22", "Name": "胸部Y"}, {"Id": "PhysicsSetting23", "Name": "裙子X"}, {"Id": "PhysicsSetting24", "Name": "裙子Y"}, {"Id": "PhysicsSetting25", "Name": "颈饰物理"}, {"Id": "PhysicsSetting26", "Name": "左身体两轴"}, {"Id": "PhysicsSetting27", "Name": "右身体两轴"}, {"Id": "PhysicsSetting28", "Name": "前发X"}, {"Id": "PhysicsSetting29", "Name": "前发Y"}, {"Id": "PhysicsSetting30", "Name": "左头饰"}, {"Id": "PhysicsSetting31", "Name": "右头饰"}, {"Id": "PhysicsSetting32", "Name": "左后发"}, {"Id": "PhysicsSetting33", "Name": "右后发"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeRPhysics"}, "VertexIndex": 2, "Scale": 1.6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.75, "Delay": 1, "Acceleration": 0.95, "Radius": 10}], "Normalization": {"Position": {"Minimum": -8, "Default": 0, "Maximum": 0}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeLPhysics"}, "VertexIndex": 2, "Scale": 1.6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.75, "Delay": 1, "Acceleration": 0.95, "Radius": 10}], "Normalization": {"Position": {"Minimum": -8, "Default": 0, "Maximum": 0}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeBallRPhysicsX"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamEyeBallRPhysicsZ"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1.2, "Acceleration": 0.75, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.89, "Delay": 1, "Acceleration": 1.2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 0}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeBallLPhysicsX"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamEyeBallLPhysicsZ"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1.2, "Acceleration": 0.75, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.89, "Delay": 1, "Acceleration": 1.2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 0}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBrowRForm"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeZ"}, "VertexIndex": 1, "Scale": 1.4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.8, "Delay": 0.8, "Acceleration": 1.5, "Radius": 35}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -40, "Default": 0, "Maximum": 40}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthForm"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeExpression2"}, "VertexIndex": 1, "Scale": 1.4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.43, "Delay": 1.1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.43, "Delay": 1.1, "Acceleration": 1.68, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -40, "Default": 0, "Maximum": 40}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBrowRY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeExpression1"}, "VertexIndex": 1, "Scale": 6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.45, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.45, "Delay": 0.9, "Acceleration": 1.68, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeBallX"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeBallPhysicsX"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.75, "Delay": 1, "Acceleration": 0.95, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeBallY"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeBallPhysicsY"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.75, "Delay": 1, "Acceleration": 0.95, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 35, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEarRPhysicsZ"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEarRPhysicsX"}, "VertexIndex": 3, "Scale": 25, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.83, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.73, "Delay": 0.85, "Acceleration": 0.9, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 35, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEarLPhysicsZ"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEarLPhysicsX"}, "VertexIndex": 3, "Scale": 25, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.83, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.73, "Delay": 0.85, "Acceleration": 0.9, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 35, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEarRPhysicsY1"}, "VertexIndex": 2, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEarRPhysicsY2"}, "VertexIndex": 3, "Scale": 28, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.83, "Delay": 0.85, "Acceleration": 1.2, "Radius": 12}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.73, "Delay": 0.85, "Acceleration": 0.9, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 35, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEarLPhysicsY1"}, "VertexIndex": 2, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEarLPhysicsY2"}, "VertexIndex": 3, "Scale": 28, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.83, "Delay": 0.85, "Acceleration": 1.2, "Radius": 12}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.73, "Delay": 0.85, "Acceleration": 0.9, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeBrowX"}, "VertexIndex": 1, "Scale": 1.4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.5, "Delay": 0.9, "Acceleration": 1.68, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -40, "Default": 0, "Maximum": 40}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 25}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.8, "Delay": 0.8, "Acceleration": 0.5, "Radius": 25}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.9, "Delay": 1.2, "Acceleration": 0.3, "Radius": 50}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 25}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.7, "Delay": 0.8, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY2"}, "VertexIndex": 1, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.83, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.83, "Delay": 0.86, "Acceleration": 0.9, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "VertexIndex": 1, "Scale": 58, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.7, "Delay": 0.8, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.7, "Delay": 0.8, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamLHandPhysics1"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamLHandPhysics2"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamLHandPhysics3"}, "VertexIndex": 4, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamLHandPhysics4"}, "VertexIndex": 5, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.7, "Delay": 1, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.7, "Delay": 1, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.6, "Delay": 1, "Acceleration": 0.78, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.6, "Delay": 1, "Acceleration": 0.74, "Radius": 6}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.6, "Delay": 1, "Acceleration": 0.7, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamRHandPhysics1"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamRHandPhysics2"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamRHandPhysics3"}, "VertexIndex": 4, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamRHandPhysics4"}, "VertexIndex": 5, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.7, "Delay": 1, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.7, "Delay": 1, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.6, "Delay": 1, "Acceleration": 0.78, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.6, "Delay": 1, "Acceleration": 0.74, "Radius": 6}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.6, "Delay": 1, "Acceleration": 0.7, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param6"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param7"}, "VertexIndex": 2, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 0.6, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.6, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.6, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param8"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param9"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 0.6, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.6, "Radius": 12}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.6, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param10"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param11"}, "VertexIndex": 2, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param28"}, "VertexIndex": 3, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 0.7, "Radius": 0}, {"Position": {"X": 0, "Y": 13.5}, "Mobility": 0.955, "Delay": 0.7, "Acceleration": 0.7, "Radius": 13.5}, {"Position": {"X": 0, "Y": 25.4}, "Mobility": 0.945, "Delay": 0.8, "Acceleration": 0.7, "Radius": 11.9}, {"Position": {"X": 0, "Y": 35.9}, "Mobility": 0.935, "Delay": 0.9, "Acceleration": 0.7, "Radius": 10.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting24", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param12"}, "VertexIndex": 1, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param13"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param14"}, "VertexIndex": 3, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.7, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 13.5}, "Mobility": 0.955, "Delay": 0.7, "Acceleration": 0.8, "Radius": 13.5}, {"Position": {"X": 0, "Y": 25.4}, "Mobility": 0.945, "Delay": 0.7, "Acceleration": 0.8, "Radius": 11.9}, {"Position": {"X": 0, "Y": 35.9}, "Mobility": 0.935, "Delay": 0.7, "Acceleration": 0.8, "Radius": 10.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -40, "Default": 0, "Maximum": 40}}}, {"Id": "PhysicsSetting25", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param30"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param31"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.83, "Delay": 0.85, "Acceleration": 1.2, "Radius": 8}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.73, "Delay": 0.85, "Acceleration": 0.9, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting26", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 28, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param17"}, "VertexIndex": 1, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param18"}, "VertexIndex": 2, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -12, "Default": 0, "Maximum": 12}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting27", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 28, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param34"}, "VertexIndex": 1, "Scale": 80, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param35"}, "VertexIndex": 2, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param36"}, "VertexIndex": 3, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.83, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.73, "Delay": 0.85, "Acceleration": 0.9, "Radius": 8}], "Normalization": {"Position": {"Minimum": -12, "Default": 0, "Maximum": 12}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting28", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param50"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param51"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param52"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.9, "Radius": 12}], "Normalization": {"Position": {"Minimum": -12, "Default": 0, "Maximum": 12}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting29", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param53"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param54"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.9, "Radius": 12}], "Normalization": {"Position": {"Minimum": -12, "Default": 0, "Maximum": 12}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting30", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param44"}, "VertexIndex": 1, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param45"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.93, "Delay": 0.95, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 0.9, "Radius": 12}], "Normalization": {"Position": {"Minimum": -12, "Default": 0, "Maximum": 12}, "Angle": {"Minimum": -15, "Default": 0, "Maximum": 15}}}, {"Id": "PhysicsSetting31", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param47"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param48"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.93, "Delay": 0.95, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 0.9, "Radius": 12}], "Normalization": {"Position": {"Minimum": -12, "Default": 0, "Maximum": 12}, "Angle": {"Minimum": -15, "Default": 0, "Maximum": 15}}}, {"Id": "PhysicsSetting32", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 25, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation11"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation12"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation13"}, "VertexIndex": 3, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation14"}, "VertexIndex": 4, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation15"}, "VertexIndex": 5, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation16"}, "VertexIndex": 6, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation17"}, "VertexIndex": 7, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation18"}, "VertexIndex": 7, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.85, "Delay": 1.3, "Acceleration": 1.2, "Radius": 13}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.87, "Delay": 1.2, "Acceleration": 1.2, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.89, "Delay": 1, "Acceleration": 1.1, "Radius": 11}, {"Position": {"X": 0, "Y": 46}, "Mobility": 0.91, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}, {"Position": {"X": 0, "Y": 56}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 66}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 76}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -12, "Default": 0, "Maximum": 12}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting33", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 25, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation19"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation20"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation21"}, "VertexIndex": 3, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation22"}, "VertexIndex": 4, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation23"}, "VertexIndex": 5, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation24"}, "VertexIndex": 6, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation25"}, "VertexIndex": 7, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation26"}, "VertexIndex": 7, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.85, "Delay": 1.3, "Acceleration": 1.2, "Radius": 13}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.87, "Delay": 1.2, "Acceleration": 1.2, "Radius": 12}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.89, "Delay": 1, "Acceleration": 1.1, "Radius": 11}, {"Position": {"X": 0, "Y": 46}, "Mobility": 0.91, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}, {"Position": {"X": 0, "Y": 56}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 66}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 76}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -12, "Default": 0, "Maximum": 12}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}]}