# -*- coding: utf-8 -*-
import sys
import os
from loguru import logger

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("MyFlowingFireflyWife 启动中...")
    logger.info(f"工作目录: {os.getcwd()}")
    logger.info(f"Python 版本: {sys.version}")

    # 确保日志目录存在
    log_dir = os.path.join(os.getcwd(), "log")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 配置日志
    logger.add("log/latest.log", rotation="500 MB")

    # 导入 Qt 相关模块（在 QApplication 创建之前）
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import Qt

    # 设置 Qt 属性（必须在 QApplication 创建之前）
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_ShareOpenGLContexts)

    # 创建 QApplication
    app = QApplication(sys.argv)

    # 现在可以安全地导入包含 Qt 对象的模块
    logger.info("导入主窗口模块...")

    # 强制重新加载模块，避免缓存问题
    import importlib

    # 清理可能的缓存模块 - 扩展清理范围
    modules_to_reload = [
        'src.window.management.ManagementWindow',
        'src.window.firefly.menu',
        'src.window.firefly.FireflyWindow',
        'src.window.message.interface',
        'src.window.loading',
        'qfluentwidgets'
    ]

    # 强制删除模块缓存
    for module_name in modules_to_reload:
        if module_name in sys.modules:
            logger.debug(f"删除模块缓存: {module_name}")
            del sys.modules[module_name]

    # 清理相关的子模块 - 扩展清理范围
    keys_to_delete = []
    for key in sys.modules.keys():
        if (key.startswith('src.window.management') or
            key.startswith('src.window.firefly') or
            key.startswith('src.window.message') or
            key.startswith('src.window.loading') or
            key.startswith('qfluentwidgets')):
            keys_to_delete.append(key)

    for key in keys_to_delete:
        logger.debug(f"删除子模块缓存: {key}")
        del sys.modules[key]

    try:
        from src.window.firefly.FireflyWindow import MainWindow
        logger.info("主窗口模块导入完成")
    except Exception as e:
        logger.error(f"导入主窗口模块失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return 1

    try:
        # 创建主窗口
        window = MainWindow(app)
        window.show()

        # 运行应用
        return app.exec()
    except Exception as e:
        logger.error(f"应用运行时出错: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
